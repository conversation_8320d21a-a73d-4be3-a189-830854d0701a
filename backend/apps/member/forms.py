from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.core.validators import EmailValidator

from .models import Member, Region


class MemberCreationForm(UserCreationForm):
    """Form for creating new members with all required fields"""

    # Personal Information
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(
            attrs={"class": "form-input", "placeholder": "First Name"}
        ),
    )
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(
            attrs={"class": "form-input", "placeholder": "Last Name"}
        ),
    )
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(
            attrs={"class": "form-input", "placeholder": "Email Address"}
        ),
    )

    # Member Details
    status = forms.ChoiceField(
        choices=[(1, "Active"), (0, "Inactive"), (2, "Pending"), (3, "Suspended")],
        initial=1,
        widget=forms.Select(attrs={"class": "form-select"}),
    )

    tier = forms.IntegerField(
        required=False,
        min_value=1,
        widget=forms.NumberInput(
            attrs={"class": "form-input", "placeholder": "Member Tier"}
        ),
    )

    # Work Address
    work_address1 = forms.CharField(
        max_length=150,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-input", "placeholder": "Work Address Line 1"}
        ),
    )
    work_address2 = forms.CharField(
        max_length=150,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-input", "placeholder": "Work Address Line 2"}
        ),
    )
    work_city = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-input", "placeholder": "Work City"}
        ),
    )
    work_state = forms.CharField(
        max_length=2,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-input", "placeholder": "Work State"}
        ),
    )
    work_postal = forms.CharField(
        max_length=10,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-input", "placeholder": "Work Postal Code"}
        ),
    )
    work_country = forms.CharField(
        max_length=20,
        required=False,
        initial="usa",
        widget=forms.TextInput(
            attrs={"class": "form-input", "placeholder": "Work Country"}
        ),
    )

    # Home Address
    home_address1 = forms.CharField(
        max_length=150,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-input", "placeholder": "Home Address Line 1"}
        ),
    )
    home_address2 = forms.CharField(
        max_length=150,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-input", "placeholder": "Home Address Line 2"}
        ),
    )
    home_city = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-input", "placeholder": "Home City"}
        ),
    )
    home_state = forms.CharField(
        max_length=2,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-input", "placeholder": "Home State"}
        ),
    )
    home_postal = forms.CharField(
        max_length=10,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-input", "placeholder": "Home Postal Code"}
        ),
    )
    home_country = forms.CharField(
        max_length=20,
        required=False,
        initial="usa",
        widget=forms.TextInput(
            attrs={"class": "form-input", "placeholder": "Home Country"}
        ),
    )

    # Contact Information
    contact_at_work = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={"class": "form-checkbox"}),
    )
    phone_work = forms.CharField(
        max_length=15,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-input", "placeholder": "Work Phone"}
        ),
    )
    phone_home = forms.CharField(
        max_length=15,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-input", "placeholder": "Home Phone"}
        ),
    )
    phone_cell = forms.CharField(
        max_length=15,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-input", "placeholder": "Cell Phone"}
        ),
    )

    # Relationships
    region = forms.ModelChoiceField(
        queryset=Region.objects.filter(can_signup=True),
        required=False,
        empty_label="Select Region",
        widget=forms.Select(attrs={"class": "form-select"}),
    )

    # Additional Settings
    lang = forms.ChoiceField(
        choices=[(1, "English"), (2, "Spanish"), (3, "French"), (4, "German")],
        initial=1,
        widget=forms.Select(attrs={"class": "form-select"}),
    )

    two_factor_auth_method = forms.ChoiceField(
        choices=[(1, "None"), (2, "SMS"), (3, "Email"), (4, "Authenticator App")],
        initial=1,
        widget=forms.Select(attrs={"class": "form-select"}),
    )

    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(
            attrs={
                "class": "form-textarea",
                "rows": 4,
                "placeholder": "Additional notes about this member...",
            }
        ),
    )

    class Meta:
        model = Member
        fields = (
            "username",
            "first_name",
            "last_name",
            "email",
            "password1",
            "password2",
            "status",
            "tier",
            "work_address1",
            "work_address2",
            "work_city",
            "work_state",
            "work_postal",
            "work_country",
            "home_address1",
            "home_address2",
            "home_city",
            "home_state",
            "home_postal",
            "home_country",
            "contact_at_work",
            "phone_work",
            "phone_home",
            "phone_cell",
            "region",
            "lang",
            "two_factor_auth_method",
            "notes",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add Tailwind classes to all form fields
        for field_name, field in self.fields.items():
            if field_name not in ["first_name", "last_name", "email", "notes"]:
                if hasattr(field.widget, "attrs"):
                    field.widget.attrs.update({"class": "form-input"})
                else:
                    field.widget.attrs = {"class": "form-input"}

    def clean_email(self):
        email = self.cleaned_data.get("email")
        if email and Member.objects.filter(email=email).exists():
            raise forms.ValidationError("A member with this email already exists.")
        return email

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data["email"]
        user.first_name = self.cleaned_data["first_name"]
        user.last_name = self.cleaned_data["last_name"]

        if commit:
            user.save()
        return user


class MemberUpdateForm(forms.ModelForm):
    """Form for updating existing members"""

    class Meta:
        model = Member
        fields = (
            "first_name",
            "last_name",
            "email",
            "status",
            "tier",
            "work_address1",
            "work_address2",
            "work_city",
            "work_state",
            "work_postal",
            "work_country",
            "home_address1",
            "home_address2",
            "home_city",
            "home_state",
            "home_postal",
            "home_country",
            "contact_at_work",
            "phone_work",
            "phone_home",
            "phone_cell",
            "region",
            "lang",
            "two_factor_auth_method",
            "notes",
        )
        widgets = {
            "first_name": forms.TextInput(attrs={"class": "form-input"}),
            "last_name": forms.TextInput(attrs={"class": "form-input"}),
            "email": forms.EmailInput(attrs={"class": "form-input"}),
            "status": forms.Select(attrs={"class": "form-select"}),
            "tier": forms.NumberInput(attrs={"class": "form-input"}),
            "work_address1": forms.TextInput(attrs={"class": "form-input"}),
            "work_address2": forms.TextInput(attrs={"class": "form-input"}),
            "work_city": forms.TextInput(attrs={"class": "form-input"}),
            "work_state": forms.TextInput(attrs={"class": "form-input"}),
            "work_postal": forms.TextInput(attrs={"class": "form-input"}),
            "work_country": forms.TextInput(attrs={"class": "form-input"}),
            "home_address1": forms.TextInput(attrs={"class": "form-input"}),
            "home_address2": forms.TextInput(attrs={"class": "form-input"}),
            "home_city": forms.TextInput(attrs={"class": "form-input"}),
            "home_state": forms.TextInput(attrs={"class": "form-input"}),
            "home_postal": forms.TextInput(attrs={"class": "form-input"}),
            "home_country": forms.TextInput(attrs={"class": "form-input"}),
            "contact_at_work": forms.CheckboxInput(attrs={"class": "form-checkbox"}),
            "phone_work": forms.TextInput(attrs={"class": "form-input"}),
            "phone_home": forms.TextInput(attrs={"class": "form-input"}),
            "phone_cell": forms.TextInput(attrs={"class": "form-input"}),
            "region": forms.Select(attrs={"class": "form-select"}),
            "lang": forms.Select(attrs={"class": "form-select"}),
            "two_factor_auth_method": forms.Select(attrs={"class": "form-select"}),
            "notes": forms.Textarea(attrs={"class": "form-textarea", "rows": 4}),
        }
