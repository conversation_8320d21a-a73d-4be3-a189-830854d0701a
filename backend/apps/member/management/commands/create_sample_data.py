from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand

from apps.member.models import Member, MemberType, Region

User = get_user_model()


class Command(BaseCommand):
    help = "Create sample data for the member system"

    def handle(self, *args, **options):
        self.stdout.write("Creating sample data...")

        # Create regions
        regions = []
        region_data = [
            {"title": "North America", "can_signup": True},
            {"title": "Europe", "can_signup": True},
            {"title": "Asia Pacific", "can_signup": True},
            {"title": "Latin America", "can_signup": False},
        ]

        for data in region_data:
            region, created = Region.objects.get_or_create(
                title=data["title"], defaults=data
            )
            regions.append(region)
            if created:
                self.stdout.write(f"Created region: {region.title}")

        # Create member types
        member_types = []
        class_data = [
            {
                "slug": "admin",
                "name": "Administrator",
                "description": "System administrators",
                "can_signup": False,
                "order": 1,
                "auto_approve": True,
                "manage_all": True,  # Flag to indicate this can manage all types
            },
            {
                "slug": "manager",
                "name": "Manager",
                "description": "Team managers",
                "can_signup": True,
                "order": 2,
                "auto_approve": True,
                "manage_all": False,
            },
            {
                "slug": "member",
                "name": "Member",
                "description": "Regular members",
                "can_signup": True,
                "order": 3,
                "auto_approve": False,
                "manage_all": False,
            },
            {
                "slug": "guest",
                "name": "Guest",
                "description": "Guest users",
                "can_signup": True,
                "order": 4,
                "auto_approve": False,
                "manage_all": False,
            },
        ]

        for data in class_data:
            manage_all = data.pop("manage_all", False)
            member_type, created = MemberType.objects.get_or_create(
                slug=data["slug"], defaults=data
            )
            member_types.append(member_type)
            if created:
                self.stdout.write(f"Created member type: {member_type.name}")
                # Set up many-to-many relationships after creation
                if manage_all:
                    # Admin can manage all types
                    member_type.can_manage_types.set(MemberType.objects.all())

        # Create a superuser if it doesn't exist
        if not User.objects.filter(username="admin").exists():
            admin_user = User.objects.create_superuser(
                username="admin",
                email="<EMAIL>",
                password="admin123",
                first_name="Admin",
                last_name="User",
                status=1,
                member_type=member_types[0],  # Administrator type
                region=regions[0],  # North America
            )
            self.stdout.write(f"Created admin user: {admin_user.username}")

        # Create some sample members
        sample_members = [
            {
                "username": "john.doe",
                "email": "<EMAIL>",
                "first_name": "John",
                "last_name": "Doe",
                "status": 1,
                "member_type": member_types[1],  # Manager
                "region": regions[0],  # North America
                "phone_cell": "******-0123",
                "work_city": "New York",
                "work_state": "NY",
            },
            {
                "username": "jane.smith",
                "email": "<EMAIL>",
                "first_name": "Jane",
                "last_name": "Smith",
                "status": 1,
                "member_type": member_types[2],  # Member
                "region": regions[1],  # Europe
                "phone_cell": "+44-20-7946-0958",
                "work_city": "London",
                "work_state": "UK",
            },
            {
                "username": "mike.wilson",
                "email": "<EMAIL>",
                "first_name": "Mike",
                "last_name": "Wilson",
                "status": 2,  # Pending
                "member_type": member_types[2],  # Member
                "region": regions[2],  # Asia Pacific
                "phone_cell": "+81-3-1234-5678",
                "work_city": "Tokyo",
                "work_state": "JP",
            },
        ]

        for member_data in sample_members:
            if not User.objects.filter(username=member_data["username"]).exists():
                member = User.objects.create_user(
                    username=member_data["username"],
                    email=member_data["email"],
                    password="password123",
                    first_name=member_data["first_name"],
                    last_name=member_data["last_name"],
                    status=member_data["status"],
                    cls=member_data["cls"],
                    region=member_data["region"],
                    phone_cell=member_data["phone_cell"],
                    work_city=member_data["work_city"],
                    work_state=member_data["work_state"],
                )
                self.stdout.write(f"Created member: {member.get_full_name()}")

        self.stdout.write(self.style.SUCCESS("Successfully created sample data!"))
