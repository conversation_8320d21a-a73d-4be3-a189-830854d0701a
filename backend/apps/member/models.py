from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.core.cache import cache
from django.db import models
from django.utils import timezone

# --- Enhanced Member Type System --- #


class MemberType(models.Model):
    """Enhanced member type with comprehensive permissions and access control"""

    name = models.CharField("Name", max_length=100, unique=True)
    slug = models.SlugField("Slug", max_length=50, unique=True)
    description = models.TextField("Description", blank=True)

    # JSON Fields for Dynamic Configuration
    permissions = models.J<PERSON><PERSON><PERSON>(
        "Permissions", default=dict, help_text="JSON object of permissions"
    )
    page_access = models.JSO<PERSON>ield(
        "Page Access",
        default=dict,
        help_text="JSON object defining accessible pages and features",
    )
    navigation = models.JSO<PERSON>ield(
        "Navigation",
        default=dict,
        help_text="JSON object defining navigation menu structure",
    )
    feature_flags = models.J<PERSON><PERSON>ield(
        "Feature Flags", default=dict, help_text="JSON object of enabled features"
    )
    dashboard_layout = models.J<PERSON><PERSON><PERSON>(
        "Dashboard Layout",
        default=dict,
        help_text="JSON object defining dashboard widget layout",
        null=True,
        blank=True,
    )
    theme_settings = models.J<PERSON><PERSON><PERSON>(
        "Theme Settings",
        default=dict,
        help_text="JSON object for UI theme customization",
        null=True,
        blank=True,
    )

    # Access Control
    can_signup = models.BooleanField("Can Signup", default=False)
    requires_approval = models.BooleanField("Requires Approval", default=True)
    auto_approve = models.BooleanField("Auto Approve", default=False)
    max_subordinates = models.PositiveIntegerField(
        "Max Subordinates", default=0, help_text="0 = unlimited"
    )
    can_manage_types = models.ManyToManyField(
        "self",
        blank=True,
        symmetrical=False,
        help_text="Member types this type can manage",
    )

    # System Settings
    is_active = models.BooleanField("Active", default=True)
    order = models.PositiveIntegerField("Display Order", default=0)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        db_table = "member_type"
        verbose_name_plural = "Member Types"
        ordering = ["order", "name"]

    def get_permissions(self):
        """Get permissions as a set for easy checking"""
        return set(self.permissions.get("permissions", []))

    def has_permission(self, permission):
        """Check if this type has a specific permission"""
        return permission in self.get_permissions()

    def get_page_access(self):
        """Get page access configuration"""
        return self.page_access.get("pages", {})

    def can_access_page(self, page_slug):
        """Check if this type can access a specific page"""
        pages = self.get_page_access()
        return pages.get(page_slug, {}).get("access", False)

    def get_navigation(self):
        """Get navigation configuration"""
        return self.navigation.get("menu", [])

    def get_feature_flags(self):
        """Get feature flags as a set"""
        return set(self.feature_flags.get("features", []))

    def has_feature(self, feature):
        """Check if this type has a specific feature enabled"""
        return feature in self.get_feature_flags()

    FIELDSETS = (
        (
            "Basic Information",
            {"fields": ("name", "slug", "description", "is_active", "order")},
        ),
        (
            "Access Control",
            {
                "fields": (
                    "can_signup",
                    "requires_approval",
                    "auto_approve",
                    "max_subordinates",
                    "can_manage_types",
                )
            },
        ),
        (
            "Permissions",
            {
                "fields": ("permissions",),
                "description": "Define what actions this member type can perform",
            },
        ),
        (
            "Page Access",
            {
                "fields": ("page_access",),
                "description": "Define which pages and features are accessible",
            },
        ),
        (
            "Navigation",
            {
                "fields": ("navigation",),
                "description": "Define the navigation menu structure",
            },
        ),
        (
            "Features",
            {
                "fields": ("feature_flags",),
                "description": "Define which features are enabled",
            },
        ),
        (
            "UI/UX",
            {
                "fields": ("dashboard_layout", "theme_settings"),
                "description": "Customize the user interface and experience",
            },
        ),
    )
    LISTDISPLAY = (
        "name",
        "slug",
        "is_active",
        "can_signup",
        "requires_approval",
        "order",
        "created",
    )


class Permission(models.Model):
    """Individual permissions that can be assigned to member types"""

    name = models.CharField("Name", max_length=100, unique=True)
    slug = models.SlugField("Slug", max_length=50, unique=True)
    description = models.TextField("Description", blank=True)
    category = models.CharField(
        "Category", max_length=50, blank=True, help_text="Group permissions by category"
    )
    is_active = models.BooleanField("Active", default=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        db_table = "permission"
        verbose_name_plural = "Permissions"
        ordering = ["category", "name"]

    FIELDSETS = (
        (
            "Permission Details",
            {"fields": ("name", "slug", "description", "category", "is_active")},
        ),
    )
    LISTDISPLAY = ("name", "slug", "category", "is_active", "created")


class PageAccess(models.Model):
    """Define page access rules for member types"""

    name = models.CharField("Name", max_length=100)
    slug = models.SlugField("Slug", max_length=50, unique=True)
    url_pattern = models.CharField(
        "URL Pattern", max_length=200, help_text="URL pattern to match"
    )
    description = models.TextField("Description", blank=True)
    category = models.CharField("Category", max_length=50, blank=True)
    requires_permission = models.CharField(
        "Required Permission", max_length=50, blank=True
    )
    is_active = models.BooleanField("Active", default=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        db_table = "page_access"
        verbose_name_plural = "Page Access Rules"
        ordering = ["category", "name"]

    FIELDSETS = (
        (
            "Page Access Details",
            {
                "fields": (
                    "name",
                    "slug",
                    "url_pattern",
                    "description",
                    "category",
                    "requires_permission",
                    "is_active",
                )
            },
        ),
    )
    LISTDISPLAY = (
        "name",
        "slug",
        "category",
        "requires_permission",
        "is_active",
        "created",
    )


# --- Region Model --- #
class Region(models.Model):
    title = models.CharField("Title", max_length=150, null=True)
    can_signup = models.BooleanField("Can Signup", default=0)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title or ""

    class Meta:
        db_table = "region"
        verbose_name_plural = "Region"
        permissions = (("region.manager", "Region Manager"),)

    FIELDSETS = (("Region Details", {"fields": ("title", "can_signup")}),)
    LISTDISPLAY = ("title", "can_signup", "created")


# --- Terms Model --- #
class Terms(models.Model):
    title = models.CharField("Title", max_length=50)
    current = models.BooleanField(default=0)
    body_text = models.TextField(blank=True, null=True)
    body_html = models.TextField(blank=True, null=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

    class Meta:
        db_table = "terms"
        verbose_name_plural = "Terms"
        permissions = (("terms.manager", "Terms Manager"),)

    FIELDSETS = (
        ("Terms Details", {"fields": ("title", "current", "body_text", "body_html")}),
    )
    LISTDISPLAY = ("title", "current", "created")


# --- Privacy Model --- #
class Privacy(models.Model):
    title = models.CharField("Title", max_length=50)
    current = models.BooleanField(default=0)
    body_text = models.TextField(blank=True, null=True)
    body_html = models.TextField(blank=True, null=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

    class Meta:
        db_table = "privacy"
        verbose_name_plural = "Privacy"
        permissions = (("privacy.manager", "Privacy Manager"),)

    FIELDSETS = (
        ("Privacy Details", {"fields": ("title", "current", "body_text", "body_html")}),
    )
    LISTDISPLAY = ("title", "current", "created")


# --- Member Model (Custom User) --- #
class Member(AbstractUser):
    # Clerk Integration
    clerk_id = models.CharField(
        "Clerk ID",
        max_length=255,
        blank=True,
        null=True,
        unique=True,
        help_text="Clerk user ID for authentication",
    )
    profile_image = models.URLField(
        "Profile Image",
        max_length=500,
        blank=True,
        null=True,
        help_text="Profile image URL from Clerk",
    )

    status = models.PositiveIntegerField("Status", default=1)
    tier = models.PositiveIntegerField("Member Tier", blank=True, null=True)
    work_address1 = models.CharField(
        "Work Address 1", max_length=150, null=True, blank=True
    )
    work_address2 = models.CharField(
        "Work Address 2", max_length=150, null=True, blank=True
    )
    work_city = models.CharField("Work City", max_length=50, null=True, blank=True)
    work_state = models.CharField(
        "Work State/Province", max_length=2, null=True, blank=True
    )
    work_postal = models.CharField("Work Postal", max_length=10, null=True, blank=True)
    work_country = models.CharField(
        "Work Country", max_length=20, null=True, blank=True, default="usa"
    )
    home_address1 = models.CharField(
        "Home Address 1", max_length=150, null=True, blank=True
    )
    home_address2 = models.CharField(
        "Home Address 2", max_length=150, null=True, blank=True
    )
    home_city = models.CharField("Home City", max_length=50, null=True, blank=True)
    home_state = models.CharField(
        "Home State/Province", max_length=2, null=True, blank=True
    )
    home_postal = models.CharField("Home Postal", max_length=10, null=True, blank=True)
    home_country = models.CharField(
        "Home Country", max_length=20, null=True, blank=True, default="usa"
    )
    contact_at_work = models.BooleanField("Contact At Work", default=0)
    phone_work = models.CharField("Work Phone", max_length=32, null=True, blank=True)
    phone_home = models.CharField("Home Phone", max_length=32, null=True, blank=True)
    phone_cell = models.CharField("Cell Phone", max_length=32, null=True, blank=True)
    region = models.ForeignKey(Region, blank=True, null=True, on_delete=models.CASCADE)

    # Enhanced Member Type System
    member_type = models.ForeignKey(
        MemberType,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="members",
    )

    approved_by = models.ForeignKey(
        "self",
        blank=True,
        null=True,
        editable=True,
        related_name="members_approved",
        on_delete=models.SET_NULL,
    )
    approved_date = models.DateTimeField(editable=False, blank=True, null=True)

    denied_by = models.ForeignKey(
        "self",
        blank=True,
        null=True,
        editable=True,
        related_name="members_denied",
        on_delete=models.SET_NULL,
    )
    denied_date = models.DateTimeField(editable=False, blank=True, null=True)

    lang = models.PositiveIntegerField("Language", default=1, blank=True, null=True)
    two_factor_auth_method = models.PositiveIntegerField(
        "2 Factor Authentication", default=1, blank=True, null=True
    )
    notes = models.TextField("Notes", null=True, blank=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.email or self.username

    # Enhanced Permission Methods
    def get_member_type(self):
        """Get the member type, fallback to class if no member_type set"""
        return self.member_type

    def has_permission(self, permission):
        """Check if member has a specific permission"""
        member_type = self.get_member_type()
        if member_type:
            return member_type.has_permission(permission)
        return False

    def can_access_page(self, page_slug):
        """Check if member can access a specific page"""
        member_type = self.get_member_type()
        if member_type:
            return member_type.can_access_page(page_slug)
        return False

    def get_navigation(self):
        """Get navigation menu for this member"""
        member_type = self.get_member_type()
        if member_type:
            return member_type.get_navigation()
        return []

    def has_feature(self, feature):
        """Check if member has access to a specific feature"""
        member_type = self.get_member_type()
        if member_type:
            return member_type.has_feature(feature)
        return False

    def get_dashboard_layout(self):
        """Get dashboard layout configuration"""
        member_type = self.get_member_type()
        if member_type:
            return member_type.dashboard_layout
        return {}

    def get_theme_settings(self):
        """Get theme settings for this member"""
        member_type = self.get_member_type()
        if member_type:
            return member_type.theme_settings
        return {}

    def can_manage_member_type(self, target_member_type):
        """Check if this member can manage a specific member type"""
        member_type = self.get_member_type()
        if member_type:
            return target_member_type in member_type.can_manage_types.all()
        return False

    def get_effective_permissions(self):
        """Get all effective permissions for this member"""
        member_type = self.get_member_type()
        if member_type:
            return member_type.get_permissions()
        return set()

    def get_member_type_features(self):
        """Get feature flags from member type"""
        if self.member_type:
            return self.member_type.get_feature_flags()
        return {}

    # --- Hierarchy Methods --- #

    def get_primary_manager(self):
        """Get the primary manager for this member"""
        try:
            return (
                self.managers.filter(is_primary=True)
                .filter(
                    models.Q(start_date__isnull=True)
                    | models.Q(start_date__lte=timezone.now().date())
                )
                .filter(
                    models.Q(end_date__isnull=True)
                    | models.Q(end_date__gt=timezone.now().date())
                )
                .first()
            )
        except Exception:
            return None

    def get_all_managers(self, relationship_type=None, active_only=True):
        """Get all managers for this member"""
        queryset = self.managers.all()
        if relationship_type:
            queryset = queryset.filter(relationship_type=relationship_type)
        if active_only:
            # Filter by date ranges for active relationships
            today = timezone.now().date()
            queryset = queryset.filter(
                models.Q(start_date__isnull=True) | models.Q(start_date__lte=today)
            ).filter(models.Q(end_date__isnull=True) | models.Q(end_date__gt=today))
        return queryset

    def get_direct_managers(self, active_only=True):
        """Get direct managers for this member"""
        return self.get_all_managers("direct_manager", active_only)

    def get_subordinates(self, relationship_type=None, active_only=True):
        """Get all subordinates for this member"""
        queryset = self.subordinates.all()

        if relationship_type:
            queryset = queryset.filter(relationship_type=relationship_type)

        if active_only:
            # Filter by date ranges for active relationships
            today = timezone.now().date()
            queryset = queryset.filter(
                models.Q(start_date__isnull=True) | models.Q(start_date__lte=today)
            ).filter(models.Q(end_date__isnull=True) | models.Q(end_date__gt=today))

        return queryset

    def get_direct_subordinates(self, active_only=True):
        """Get direct subordinates for this member"""
        return self.get_subordinates("direct_manager", active_only)

    def get_hierarchy_tree(self, max_depth=3):
        """Get a hierarchical tree of this member's organization"""

        def build_tree(member, depth=0):
            if depth >= max_depth:
                return None

            subordinates = member.get_subordinates()

            tree = {"member": member, "subordinates": []}

            for sub in subordinates:
                sub_tree = build_tree(sub.member, depth + 1)
                if sub_tree:
                    tree["subordinates"].append(sub_tree)

            return tree

        return build_tree(self)

    def is_manager_of(self, other_member):
        """Check if this member is a manager of the other member"""
        today = timezone.now().date()
        return (
            self.subordinates.filter(member=other_member)
            .filter(models.Q(start_date__isnull=True) | models.Q(start_date__lte=today))
            .filter(models.Q(end_date__isnull=True) | models.Q(end_date__gt=today))
            .exists()
        )

    def is_subordinate_of(self, other_member):
        """Check if this member is a subordinate of the other member"""
        today = timezone.now().date()
        return (
            self.managers.filter(manager=other_member)
            .filter(models.Q(start_date__isnull=True) | models.Q(start_date__lte=today))
            .filter(models.Q(end_date__isnull=True) | models.Q(end_date__gt=today))
            .exists()
        )

    # --- Team Methods --- #

    def get_teams(self, active_only=True):
        """Get all teams this member belongs to"""
        queryset = self.team_memberships.all()
        if active_only:
            today = timezone.now().date()
            queryset = queryset.filter(
                models.Q(start_date__isnull=True) | models.Q(start_date__lte=today)
            ).filter(models.Q(end_date__isnull=True) | models.Q(end_date__gt=today))
        return queryset

    def get_primary_team(self):
        """Get the primary team for this member"""
        try:
            return (
                self.team_memberships.filter(is_primary=True)
                .filter(
                    models.Q(start_date__isnull=True)
                    | models.Q(start_date__lte=timezone.now().date())
                )
                .filter(
                    models.Q(end_date__isnull=True)
                    | models.Q(end_date__gt=timezone.now().date())
                )
                .first()
            )
        except Exception:
            return None

    def get_team_memberships(self, active_only=True):
        """Get all team memberships for this member"""
        queryset = self.team_memberships.all()
        if active_only:
            today = timezone.now().date()
            queryset = queryset.filter(
                models.Q(start_date__isnull=True) | models.Q(start_date__lte=today)
            ).filter(models.Q(end_date__isnull=True) | models.Q(end_date__gt=today))
        return queryset

    def is_team_lead(self, team=None):
        """Check if this member is a team lead"""
        queryset = self.team_memberships.filter(role="lead")
        if team:
            queryset = queryset.filter(team=team)
        return queryset.exists()

    def get_team_roles(self):
        """Get all team roles for this member"""
        return self.team_memberships.values_list("team__name", "role")

    def get_teams_by_role(self, role):
        """Get teams where member has a specific role"""
        return self.team_memberships.filter(role=role).values_list("team", flat=True)

    def add_to_team(
        self,
        team,
        role="member",
        is_primary=False,
        start_date=None,
        end_date=None,
        notes="",
    ):
        """Add member to a team"""
        membership, created = MemberTeam.objects.get_or_create(
            member=self,
            team=team,
            defaults={
                "role": role,
                "is_primary": is_primary,
                "start_date": start_date,
                "end_date": end_date,
                "notes": notes,
            },
        )
        return membership, created

    def remove_from_team(self, team):
        """Remove member from a team"""
        return self.team_memberships.filter(team=team).delete()

    def get_team_members(self, team):
        """Get all members in the same team"""
        return team.members.exclude(id=self.id)

    # --- Terms Acceptance Methods --- #

    def has_accepted_terms(self, terms=None):
        """Check if member has accepted the specified terms (or current terms if none specified)"""
        if terms is None:
            # Single query using join for current terms
            return self.terms_logs.filter(terms__current=True).exists()
        else:

            return self.terms_logs.filter(terms=terms).exists()

    def get_latest_terms_accepted(self):
        """Get the latest terms version this member has accepted"""
        latest_log = self.terms_logs.order_by("-accepted_at").first()
        return latest_log.terms if latest_log else None

    def accept_terms(self, terms=None):
        """Accept the specified terms (or current terms if none specified)"""
        if terms is None:
            terms = Terms.objects.filter(current=True).first()
            if not terms:
                raise ValueError("No current terms available to accept")

        # Create or update the acceptance log
        log, created = MemberTermsLog.objects.get_or_create(
            member=self, terms=terms, defaults={"accepted_at": timezone.now()}
        )
        return log, created

    # --- Privacy Acceptance Methods --- #

    def has_accepted_privacy(self, privacy=None):
        """Check if member has accepted the specified privacy policy (or current privacy if none specified)"""
        if privacy is None:
            # Single query using join for current privacy
            return self.privacy_logs.filter(privacy__current=True).exists()
        else:
            # Specific privacy check
            return self.privacy_logs.filter(privacy=privacy).exists()

    def get_latest_privacy_accepted(self):
        """Get the latest privacy policy version this member has accepted"""
        latest_log = self.privacy_logs.order_by("-accepted_at").first()
        return latest_log.privacy if latest_log else None

    def accept_privacy(self, privacy=None):
        """Accept the specified privacy policy (or current privacy if none specified)"""
        if privacy is None:
            privacy = Privacy.objects.filter(current=True).first()
            if not privacy:
                raise ValueError("No current privacy policy available to accept")

        # Create or update the acceptance log
        log, created = MemberPrivacyLog.objects.get_or_create(
            member=self, privacy=privacy, defaults={"accepted_at": timezone.now()}
        )
        return log, created

    def get_current_terms(self):
        """Cached lookup for current terms"""
        cache_key = "current_terms"
        terms = cache.get(cache_key)
        if terms is None:
            terms = Terms.objects.filter(current=True).first()
            if terms:
                cache.set(cache_key, terms, 300)
        return terms

    def get_current_privacy(self):
        """Cached lookup for current privacy policy"""
        cache_key = "current_privacy"
        privacy = cache.get(cache_key)
        if privacy is None:
            privacy = Privacy.objects.filter(current=True).first()
            if privacy:
                cache.set(cache_key, privacy, 300)
        return privacy

    class Meta:
        db_table = "member"
        verbose_name_plural = "Member"
        permissions = (("member.manager", "Member Manager"),)
        ordering = ("email",)

    FIELDSETS = (
        (
            "Clerk Integration",
            {
                "fields": ("clerk_id", "profile_image"),
                "description": "Clerk authentication and profile information",
            },
        ),
        (
            "Member Details",
            {
                "fields": (
                    "username",
                    "email",
                    "status",
                    "member_type",
                    "region",
                    "tier",
                    "work_address1",
                    "work_address2",
                    "work_city",
                    "work_state",
                    "work_postal",
                    "work_country",
                    "home_address1",
                    "home_address2",
                    "home_city",
                    "home_state",
                    "home_postal",
                    "home_country",
                    "phone_work",
                    "phone_home",
                    "phone_cell",
                    "last_login",
                    "approved_by",
                    "approved_date",
                    "denied_by",
                    "denied_date",
                    "lang",
                    "two_factor_auth_method",
                    "feature_flags",
                    "notes",
                )
            },
        ),
        (
            "Terms Acceptance",
            {
                "fields": ("last_accepted_terms",),
                "description": "Terms and conditions acceptance status for this member",
            },
        ),
        (
            "Privacy Acceptance",
            {
                "fields": ("last_accepted_privacy",),
                "description": "Privacy policy acceptance status for this member",
            },
        ),
    )
    LISTDISPLAY = (
        "username",
        "email",
        "member_type",
        "phone_cell",
        "status",
        "created",
    )


# --- MemberAuthToken Model --- #
class MemberAuthToken(models.Model):
    member = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="auth_tokens"
    )
    auth_type = models.CharField("Auth Type", max_length=10)
    token = models.CharField(
        "Token", max_length=255, blank=True, null=True, editable=False
    )
    issued = models.DateTimeField("Issued At", blank=True, null=True, editable=False)
    expires = models.DateTimeField("Expires At", blank=True, null=True, editable=False)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "member_auth_token"
        verbose_name_plural = "Member Auth Tokens"
        ordering = ["-issued"]

    def __str__(self):
        return f"{self.auth_type} token for {self.member}"


# --- MemberTermsLog Model --- #
class MemberTermsLog(models.Model):
    member = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="terms_logs"
    )
    terms = models.ForeignKey(
        Terms, on_delete=models.CASCADE, related_name="member_logs"
    )
    accepted_at = models.DateTimeField(auto_now_add=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "member_terms_log"
        verbose_name_plural = "Member Terms Log"
        unique_together = ("member", "terms")
        indexes = [
            models.Index(fields=["member", "-accepted_at"]),
            models.Index(fields=["terms", "-accepted_at"]),
        ]

    def __str__(self):
        return f"{self.member} accepted {self.terms.title} on {self.accepted_at.strftime('%Y-%m-%d')}"


# --- MemberPrivacyLog Model --- #
class MemberPrivacyLog(models.Model):
    member = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="privacy_logs"
    )
    privacy = models.ForeignKey(
        Privacy, on_delete=models.CASCADE, related_name="member_logs"
    )
    accepted_at = models.DateTimeField(auto_now_add=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "member_privacy_log"
        verbose_name_plural = "Member Privacy Log"
        unique_together = ("member", "privacy")
        indexes = [
            models.Index(fields=["member", "-accepted_at"]),
            models.Index(fields=["privacy", "-accepted_at"]),
        ]

    def __str__(self):
        return f"{self.member} accepted {self.privacy.title} on {self.accepted_at.strftime('%Y-%m-%d')}"


# --- W9 Upload Model --- #
def w9_upload_path(instance, filename):
    return f"w9_uploads/member_{instance.member.id}/{filename}"


class MemberW9Upload(models.Model):
    member = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="w9_uploads"
    )
    file = models.FileField(upload_to=w9_upload_path)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "member_w9_upload"
        verbose_name_plural = "W9 Uploads"
        ordering = ["-uploaded_at"]

    def __str__(self):
        return f"W9 for {self.member} uploaded on {self.uploaded_at.strftime('%Y-%m-%d %H:%M:%S')}"


# --- Communication Model --- #
class Communication(models.Model):
    COMMUNICATION_TYPE_CHOICES = [
        ("email", "Email"),
        ("notification", "Notification"),
        ("message", "Message"),
        ("sms", "SMS"),
        ("push", "Push Notification"),
    ]
    STATUS_CHOICES = [
        ("pending", "Pending"),
        ("sent", "Sent"),
        ("delivered", "Delivered"),
        ("read", "Read"),
        ("failed", "Failed"),
        ("cancelled", "Cancelled"),
    ]
    PRIORITY_CHOICES = [
        ("low", "Low"),
        ("normal", "Normal"),
        ("high", "High"),
        ("urgent", "Urgent"),
    ]
    communication_type = models.CharField(
        "Type", max_length=20, choices=COMMUNICATION_TYPE_CHOICES, default="email"
    )
    status = models.CharField(
        "Status", max_length=20, choices=STATUS_CHOICES, default="pending"
    )
    priority = models.CharField(
        "Priority", max_length=10, choices=PRIORITY_CHOICES, default="normal"
    )
    to_member = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="received_communications",
        null=True,
        blank=True,
    )
    to_email = models.EmailField("To Email", max_length=200, null=True, blank=True)
    to_phone = models.CharField("To Phone", max_length=20, null=True, blank=True)
    from_member = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="sent_communications",
        null=True,
        blank=True,
    )
    from_email = models.EmailField("From Email", max_length=200, null=True, blank=True)
    from_name = models.CharField("From Name", max_length=100, null=True, blank=True)
    subject = models.CharField("Subject", max_length=200, null=True, blank=True)
    title = models.CharField("Title", max_length=200, null=True, blank=True)
    content = models.TextField("Content")
    content_html = models.TextField("HTML Content", null=True, blank=True)
    template_name = models.CharField("Template", max_length=100, null=True, blank=True)
    category = models.CharField("Category", max_length=50, null=True, blank=True)
    tags = models.CharField(
        "Tags", max_length=255, null=True, blank=True, help_text="Comma-separated tags"
    )
    sent_at = models.DateTimeField("Sent At", null=True, blank=True)
    delivered_at = models.DateTimeField("Delivered At", null=True, blank=True)
    read_at = models.DateTimeField("Read At", null=True, blank=True)
    failed_at = models.DateTimeField("Failed At", null=True, blank=True)
    failure_reason = models.TextField("Failure Reason", null=True, blank=True)
    retry_count = models.PositiveIntegerField("Retry Count", default=0)
    max_retries = models.PositiveIntegerField("Max Retries", default=3)
    scheduled_for = models.DateTimeField("Scheduled For", null=True, blank=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.communication_type}: {self.from_member} -> {self.to_member}"

    def to_member_username(self):
        """Get the username of the recipient"""
        if self.to_member:
            return self.to_member.username
        elif self.to_email:
            return self.to_email
        else:
            return "N/A"

    to_member_username.short_description = "To"

    def from_member_username(self):
        """Get the username of the sender"""
        if self.from_member:
            return self.from_member.username
        elif self.from_name:
            return self.from_name
        elif self.from_email:
            return self.from_email
        else:
            return "N/A"

    from_member_username.short_description = "From"

    class Meta:
        db_table = "communication"
        verbose_name_plural = "Communications"
        permissions = (
            ("communication.manager", "Communication Manager"),
            ("communication.view_all", "View All Communications"),
            ("communication.send", "Send Communications"),
        )
        ordering = ["-created"]
        indexes = [
            models.Index(fields=["communication_type", "status"]),
            models.Index(fields=["to_member", "status"]),
            models.Index(fields=["scheduled_for", "status"]),
            models.Index(fields=["created"]),
        ]

    FIELDSETS = (
        (
            "Communication Details",
            {
                "fields": (
                    "communication_type",
                    "status",
                    "priority",
                    "category",
                    "template_name",
                )
            },
        ),
        ("Recipients", {"fields": ("to_member", "to_email", "to_phone")}),
        ("Sender", {"fields": ("from_member", "from_email", "from_name")}),
        ("Content", {"fields": ("subject", "title", "content", "content_html")}),
        (
            "Delivery",
            {
                "fields": (
                    "sent_at",
                    "delivered_at",
                    "read_at",
                    "failed_at",
                    "failure_reason",
                )
            },
        ),
        (
            "Scheduling & Retries",
            {"fields": ("scheduled_for", "retry_count", "max_retries")},
        ),
        ("Metadata", {"fields": ("tags", "created", "modified")}),
    )
    LISTDISPLAY = (
        "communication_type",
        "status",
        "priority",
        "to_member_username",
        "from_member_username",
        "created",
    )


# --- EmailTemplate Model --- #
class EmailTemplate(models.Model):
    TEMPLATE_TYPE_CHOICES = [
        ("email", "Email Template"),
        ("notification", "Notification Template"),
        ("message", "Message Template"),
        ("sms", "SMS Template"),
    ]
    CATEGORY_CHOICES = [
        ("welcome", "Welcome"),
        ("password_reset", "Password Reset"),
        ("notification", "Notification"),
        ("marketing", "Marketing"),
        ("system", "System"),
        ("custom", "Custom"),
    ]
    name = models.CharField("Template Name", max_length=100)
    template_type = models.CharField(
        "Type", max_length=20, choices=TEMPLATE_TYPE_CHOICES, default="email"
    )
    category = models.CharField(
        "Category", max_length=20, choices=CATEGORY_CHOICES, default="custom"
    )
    subject = models.CharField("Subject", max_length=200, null=True, blank=True)
    title = models.CharField("Title", max_length=200, null=True, blank=True)
    content_text = models.TextField("Plain Text Content", null=True, blank=True)
    content_html = models.TextField("HTML Content", null=True, blank=True)
    variables = models.JSONField(
        "Template Variables",
        default=dict,
        blank=True,
        help_text="Available variables for this template",
    )
    is_active = models.BooleanField("Active", default=True)
    is_system = models.BooleanField(
        "System Template", default=False, help_text="System templates cannot be deleted"
    )
    description = models.TextField("Description", null=True, blank=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="created_templates",
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"

    class Meta:
        db_table = "email_template"
        verbose_name_plural = "Email Templates"
        ordering = ["name"]
        permissions = (
            ("template.manager", "Template Manager"),
            ("template.create", "Create Templates"),
            ("template.edit", "Edit Templates"),
        )

    FIELDSETS = (
        (
            "Template Details",
            {"fields": ("name", "template_type", "category", "description")},
        ),
        ("Content", {"fields": ("subject", "title", "content_text", "content_html")}),
        ("Settings", {"fields": ("is_active", "is_system", "variables")}),
        ("Metadata", {"fields": ("created_by", "created", "modified")}),
    )
    LISTDISPLAY = (
        "name",
        "template_type",
        "category",
        "is_active",
        "created_by",
        "created",
    )


# --- TemplateBuilder Model --- #
class TemplateBuilder(models.Model):
    COMPONENT_TYPE_CHOICES = [
        ("header", "Header"),
        ("text", "Text Block"),
        ("button", "Button"),
        ("image", "Image"),
        ("divider", "Divider"),
        ("footer", "Footer"),
        ("custom", "Custom HTML"),
    ]
    template = models.ForeignKey(
        EmailTemplate, on_delete=models.CASCADE, related_name="builder_components"
    )
    component_type = models.CharField(
        "Component Type", max_length=20, choices=COMPONENT_TYPE_CHOICES
    )
    order = models.PositiveIntegerField("Order", default=0)
    config = models.JSONField("Configuration", default=dict)
    content = models.TextField("Content", null=True, blank=True)
    css_classes = models.CharField("CSS Classes", max_length=255, null=True, blank=True)
    inline_styles = models.TextField("Inline Styles", null=True, blank=True)
    is_active = models.BooleanField("Active", default=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    def __str__(self):
        return (
            f"{self.template.name} - {self.get_component_type_display()} ({self.order})"
        )

    class Meta:
        db_table = "template_builder"
        verbose_name_plural = "Template Builder Components"
        ordering = ["template", "order"]
        unique_together = ("template", "order")


# --- PasswordReset Model --- #
class PasswordReset(models.Model):
    member = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    link = models.CharField(max_length=150)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.link

    class Meta:
        db_table = "member_password_reset"
        verbose_name_plural = "Member Password Reset"
        permissions = (
            ("member_password_reset.manager", "Member Password Reset Manager"),
        )

    FIELDSETS = (("Member Password Reset", {"fields": ("member", "link")}),)
    LISTDISPLAY = ("member", "link", "created")


class MemberHierarchy(models.Model):
    """Model to handle multiple managers and hierarchical relationships between members"""

    RELATIONSHIP_TYPES = [
        ("direct_manager", "Direct Manager"),
        ("indirect_manager", "Indirect Manager"),
        ("mentor", "Mentor"),
        ("supervisor", "Supervisor"),
        ("team_lead", "Team Lead"),
        ("project_manager", "Project Manager"),
    ]

    member = models.ForeignKey(
        Member, on_delete=models.CASCADE, related_name="managers"
    )
    manager = models.ForeignKey(
        Member, on_delete=models.CASCADE, related_name="subordinates"
    )
    relationship_type = models.CharField(
        max_length=20, choices=RELATIONSHIP_TYPES, default="direct_manager"
    )
    is_primary = models.BooleanField(
        default=False, help_text="Mark as primary manager for this member"
    )
    start_date = models.DateField(
        null=True, blank=True, help_text="When this relationship started"
    )
    end_date = models.DateField(
        null=True, blank=True, help_text="When this relationship ended (if applicable)"
    )
    notes = models.TextField(
        blank=True, help_text="Additional notes about this relationship"
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Member Hierarchy"
        verbose_name_plural = "Member Hierarchies"
        unique_together = ["member", "manager", "relationship_type"]
        ordering = ["-is_primary", "relationship_type", "created"]

    def __str__(self):
        return (
            f"{self.member} -> {self.manager} ({self.get_relationship_type_display()})"
        )

    @property
    def is_active(self):
        """Check if this relationship is currently active"""
        today = timezone.now().date()
        if self.end_date and self.end_date < today:
            return False
        if self.start_date and self.start_date > today:
            return False
        return True


class Team(models.Model):
    """Model to organize members into teams"""

    TEAM_TYPES = [
        ("distributor", "Distributor"),
        ("custom", "Custom"),
        ("sales", "Sales"),
        ("support", "Support"),
        ("development", "Development"),
        ("marketing", "Marketing"),
        ("operations", "Operations"),
        ("finance", "Finance"),
        ("hr", "Human Resources"),
        ("legal", "Legal"),
        ("other", "Other"),
    ]

    name = models.CharField(max_length=100, unique=True)
    team_type = models.CharField(max_length=20, choices=TEAM_TYPES, default="custom")
    description = models.TextField(blank=True, help_text="Team description and purpose")
    team_lead = models.ForeignKey(
        "Member",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="led_teams",
        help_text="Team leader",
    )
    is_active = models.BooleanField(
        default=True, help_text="Whether this team is currently active"
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Team"
        verbose_name_plural = "Teams"
        ordering = ["name"]

    def __str__(self):
        return f"{self.name} ({self.get_team_type_display()})"

    def get_members(self):
        """Get all members in this team"""
        return Member.objects.filter(team_memberships__team=self)

    def get_active_members(self):
        """Get all active members in this team"""
        today = timezone.now().date()
        return Member.objects.filter(
            team_memberships__team=self,
            team_memberships__start_date__lte=today,
            team_memberships__end_date__isnull=True,
        ) | Member.objects.filter(
            team_memberships__team=self,
            team_memberships__start_date__isnull=True,
            team_memberships__end_date__isnull=True,
        )

    def get_member_count(self):
        """Get the number of members in this team"""
        return self.member_teams.count()

    def get_memberships(self, active_only=True):
        """Get all team memberships"""
        queryset = self.member_teams.all()
        if active_only:
            today = timezone.now().date()
            queryset = queryset.filter(
                models.Q(start_date__isnull=True) | models.Q(start_date__lte=today)
            ).filter(models.Q(end_date__isnull=True) | models.Q(end_date__gt=today))
        return queryset


class MemberTeam(models.Model):
    """Intermediate model for Member-Team relationship with additional metadata"""

    ROLE_CHOICES = [
        ("admin", "Admin"),
        ("contributor", "Contributor"),
        ("manager", "Manager"),
        ("member", "Member"),
        ("observer", "Observer"),
        ("other", "Other"),
        ("salesrep", "Sales Rep"),
        ("lead", "Team Lead"),
    ]

    member = models.ForeignKey(
        "Member", on_delete=models.CASCADE, related_name="team_memberships"
    )
    team = models.ForeignKey(
        Team, on_delete=models.CASCADE, related_name="member_teams"
    )
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default="member")
    is_primary = models.BooleanField(
        default=False, help_text="Mark as primary team for this member"
    )
    start_date = models.DateField(
        null=True, blank=True, help_text="When this membership started"
    )
    end_date = models.DateField(
        null=True, blank=True, help_text="When this membership ended (if applicable)"
    )
    notes = models.TextField(
        blank=True, help_text="Additional notes about this team membership"
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Member Team"
        verbose_name_plural = "Member Teams"
        unique_together = ["member", "team"]
        ordering = ["-is_primary", "role", "created"]

    def __str__(self):
        return f"{self.member} -> {self.team} ({self.get_role_display()})"

    @property
    def is_active(self):
        """Check if this membership is currently active"""
        today = timezone.now().date()
        if self.end_date and self.end_date < today:
            return False
        if self.start_date and self.start_date > today:
            return False
        return True
