{% extends 'member/base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">{{ title }}</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">
            {% if member %}
                Update member information below.
            {% else %}
                Create a new member by filling out the form below.
            {% endif %}
        </p>
    </div>

    <div class="border-t border-gray-200">
        <form method="post" class="space-y-6 p-6">
            {% csrf_token %}

            <!-- Personal Information -->
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Personal Information</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <div>
                            <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700">Username</label>
                            {{ form.username }}
                            {% if form.username.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.username.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700">Email</label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.email.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700">First Name</label>
                            {{ form.first_name }}
                            {% if form.first_name.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.first_name.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Last Name</label>
                            {{ form.last_name }}
                            {% if form.last_name.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.last_name.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </dd>
            </div>

            <!-- Member Details -->
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Member Details</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-3">
                        <div>
                            <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-gray-700">Status</label>
                            {{ form.status }}
                            {% if form.status.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.status.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.tier.id_for_label }}" class="block text-sm font-medium text-gray-700">Tier</label>
                            {{ form.tier }}
                            {% if form.tier.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.tier.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.cls.id_for_label }}" class="block text-sm font-medium text-gray-700">Class</label>
                            {{ form.cls }}
                            {% if form.cls.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.cls.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </dd>
            </div>

            <!-- Work Address -->
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Work Address</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <div class="sm:col-span-2">
                            <label for="{{ form.work_address1.id_for_label }}" class="block text-sm font-medium text-gray-700">Address Line 1</label>
                            {{ form.work_address1 }}
                            {% if form.work_address1.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.work_address1.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div class="sm:col-span-2">
                            <label for="{{ form.work_address2.id_for_label }}" class="block text-sm font-medium text-gray-700">Address Line 2</label>
                            {{ form.work_address2 }}
                            {% if form.work_address2.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.work_address2.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.work_city.id_for_label }}" class="block text-sm font-medium text-gray-700">City</label>
                            {{ form.work_city }}
                            {% if form.work_city.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.work_city.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.work_state.id_for_label }}" class="block text-sm font-medium text-gray-700">State</label>
                            {{ form.work_state }}
                            {% if form.work_state.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.work_state.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.work_postal.id_for_label }}" class="block text-sm font-medium text-gray-700">Postal Code</label>
                            {{ form.work_postal }}
                            {% if form.work_postal.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.work_postal.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.work_country.id_for_label }}" class="block text-sm font-medium text-gray-700">Country</label>
                            {{ form.work_country }}
                            {% if form.work_country.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.work_country.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </dd>
            </div>

            <!-- Home Address -->
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Home Address</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <div class="sm:col-span-2">
                            <label for="{{ form.home_address1.id_for_label }}" class="block text-sm font-medium text-gray-700">Address Line 1</label>
                            {{ form.home_address1 }}
                            {% if form.home_address1.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.home_address1.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div class="sm:col-span-2">
                            <label for="{{ form.home_address2.id_for_label }}" class="block text-sm font-medium text-gray-700">Address Line 2</label>
                            {{ form.home_address2 }}
                            {% if form.home_address2.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.home_address2.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.home_city.id_for_label }}" class="block text-sm font-medium text-gray-700">City</label>
                            {{ form.home_city }}
                            {% if form.home_city.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.home_city.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.home_state.id_for_label }}" class="block text-sm font-medium text-gray-700">State</label>
                            {{ form.home_state }}
                            {% if form.home_state.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.home_state.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.home_postal.id_for_label }}" class="block text-sm font-medium text-gray-700">Postal Code</label>
                            {{ form.home_postal }}
                            {% if form.home_postal.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.home_postal.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.home_country.id_for_label }}" class="block text-sm font-medium text-gray-700">Country</label>
                            {{ form.home_country }}
                            {% if form.home_country.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.home_country.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </dd>
            </div>

            <!-- Contact Information -->
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Contact Information</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <div>
                            <label for="{{ form.phone_work.id_for_label }}" class="block text-sm font-medium text-gray-700">Work Phone</label>
                            {{ form.phone_work }}
                            {% if form.phone_work.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.phone_work.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.phone_home.id_for_label }}" class="block text-sm font-medium text-gray-700">Home Phone</label>
                            {{ form.phone_home }}
                            {% if form.phone_home.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.phone_home.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.phone_cell.id_for_label }}" class="block text-sm font-medium text-gray-700">Cell Phone</label>
                            {{ form.phone_cell }}
                            {% if form.phone_cell.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.phone_cell.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div class="flex items-center">
                            <label for="{{ form.contact_at_work.id_for_label }}" class="flex items-center">
                                {{ form.contact_at_work }}
                                <span class="ml-2 text-sm text-gray-700">Contact at Work</span>
                            </label>
                            {% if form.contact_at_work.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.contact_at_work.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </dd>
            </div>

            <!-- Relationships -->
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Relationships</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-3">
                        <div>
                            <label for="{{ form.manager.id_for_label }}" class="block text-sm font-medium text-gray-700">Manager</label>
                            {{ form.manager }}
                            {% if form.manager.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.manager.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.region.id_for_label }}" class="block text-sm font-medium text-gray-700">Region</label>
                            {{ form.region }}
                            {% if form.region.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.region.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.cls.id_for_label }}" class="block text-sm font-medium text-gray-700">Class</label>
                            {{ form.cls }}
                            {% if form.cls.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.cls.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </dd>
            </div>

            <!-- Settings -->
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Settings</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <div>
                            <label for="{{ form.lang.id_for_label }}" class="block text-sm font-medium text-gray-700">Language</label>
                            {{ form.lang }}
                            {% if form.lang.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.lang.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.two_factor_auth_method.id_for_label }}" class="block text-sm font-medium text-gray-700">2FA Method</label>
                            {{ form.two_factor_auth_method }}
                            {% if form.two_factor_auth_method.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.two_factor_auth_method.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </dd>
            </div>

            <!-- Notes -->
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Notes</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <div>
                        <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700">Additional Notes</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.notes.errors.0 }}</p>
                        {% endif %}
                    </div>
                </dd>
            </div>

            <!-- Password Fields (only for creation) -->
            {% if not member %}
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Password</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <div>
                            <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700">Password</label>
                            {{ form.password1 }}
                            {% if form.password1.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.password1.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                            {{ form.password2 }}
                            {% if form.password2.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.password2.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </dd>
            </div>
            {% endif %}

            <!-- Form Actions -->
            <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
                <a href="{% url 'member:member_list' %}" class="btn-secondary mr-3">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    {{ submit_text }}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
