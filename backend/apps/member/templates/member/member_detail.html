{% extends 'member/base.html' %}

{% block title %}Member Details: {{ member.get_full_name }}{% endblock %}

{% block content %}
<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <div class="flex justify-between items-center">
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Member Details</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    Complete information for {{ member.get_full_name }}
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'member:member_update' member.pk %}" class="btn-primary">
                    Edit Member
                </a>
                <a href="{% url 'member:member_hierarchy' member.id %}" class="btn-secondary">
                    View Hierarchy
                </a>
                <a href="{% url 'member:member_list' %}" class="btn-secondary">
                    Back to List
                </a>
            </div>
        </div>
    </div>

    <div class="border-t border-gray-200">
        <!-- Personal Information -->
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Personal Information</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                        <span class="font-medium">Username:</span> {{ member.username }}
                    </div>
                    <div>
                        <span class="font-medium">Email:</span> {{ member.email }}
                    </div>
                    <div>
                        <span class="font-medium">First Name:</span> {{ member.first_name|default:"-" }}
                    </div>
                    <div>
                        <span class="font-medium">Last Name:</span> {{ member.last_name|default:"-" }}
                    </div>
                    <div>
                        <span class="font-medium">Status:</span>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            {% if member.status == 1 %}bg-green-100 text-green-800
                            {% elif member.status == 0 %}bg-red-100 text-red-800
                            {% elif member.status == 2 %}bg-yellow-100 text-yellow-800
                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {% if member.status == 1 %}Active
                            {% elif member.status == 0 %}Inactive
                            {% elif member.status == 2 %}Pending
                            {% else %}Suspended{% endif %}
                        </span>
                    </div>
                    <div>
                        <span class="font-medium">Tier:</span> {{ member.tier|default:"-" }}
                    </div>
                </div>
            </dd>
        </div>

        <!-- Work Address -->
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Work Address</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {% if member.work_address1 or member.work_city %}
                    <div class="space-y-1">
                        {% if member.work_address1 %}<div>{{ member.work_address1 }}</div>{% endif %}
                        {% if member.work_address2 %}<div>{{ member.work_address2 }}</div>{% endif %}
                        <div>
                            {% if member.work_city %}{{ member.work_city }}{% endif %}
                            {% if member.work_state %}, {{ member.work_state }}{% endif %}
                            {% if member.work_postal %} {{ member.work_postal }}{% endif %}
                        </div>
                        {% if member.work_country %}<div>{{ member.work_country }}</div>{% endif %}
                    </div>
                {% else %}
                    <span class="text-gray-400">No work address provided</span>
                {% endif %}
            </dd>
        </div>

        <!-- Home Address -->
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Home Address</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {% if member.home_address1 or member.home_city %}
                    <div class="space-y-1">
                        {% if member.home_address1 %}<div>{{ member.home_address1 }}</div>{% endif %}
                        {% if member.home_address2 %}<div>{{ member.home_address2 }}</div>{% endif %}
                        <div>
                            {% if member.home_city %}{{ member.home_city }}{% endif %}
                            {% if member.home_state %}, {{ member.home_state }}{% endif %}
                            {% if member.home_postal %} {{ member.home_postal }}{% endif %}
                        </div>
                        {% if member.home_country %}<div>{{ member.home_country }}</div>{% endif %}
                    </div>
                {% else %}
                    <span class="text-gray-400">No home address provided</span>
                {% endif %}
            </dd>
        </div>

        <!-- Contact Information -->
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Contact Information</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                        <span class="font-medium">Work Phone:</span> {{ member.phone_work|default:"-" }}
                    </div>
                    <div>
                        <span class="font-medium">Home Phone:</span> {{ member.phone_home|default:"-" }}
                    </div>
                    <div>
                        <span class="font-medium">Cell Phone:</span> {{ member.phone_cell|default:"-" }}
                    </div>
                    <div>
                        <span class="font-medium">Contact at Work:</span>
                        {% if member.contact_at_work %}Yes{% else %}No{% endif %}
                    </div>
                </div>
            </dd>
        </div>

        <!-- Relationships -->
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Relationships</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                        <span class="font-medium">Primary Manager:</span>
                        {% with primary_manager=member.get_primary_manager %}
                            {% if primary_manager %}
                                <a href="{% url 'member:member_detail' primary_manager.manager.id %}" class="text-blue-600 hover:text-blue-800">
                                    {{ primary_manager.manager.get_full_name }}
                                </a>
                            {% else %}
                                -
                            {% endif %}
                        {% endwith %}
                    </div>
                    <div>
                        <span class="font-medium">Region:</span>
                        {% if member.region %}
                            {{ member.region.title }}
                        {% else %}
                            -
                        {% endif %}
                    </div>
                    <div>
                        <span class="font-medium">Class:</span>
                        {% if member.cls %}
                            {{ member.cls.classname }} ({{ member.cls.description }})
                        {% else %}
                            -
                        {% endif %}
                    </div>
                </div>
            </dd>
        </div>

        <!-- Team Memberships -->
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Team Memberships</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {% with team_memberships=member.get_team_memberships %}
                    {% if team_memberships %}
                        <div class="space-y-3">
                            {% for membership in team_memberships %}
                            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <a href="{% url 'member:team_detail' membership.team.id %}" class="font-medium text-blue-600 hover:text-blue-800">
                                        {{ membership.team.name }}
                                    </a>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        {{ membership.get_role_display }}
                                    </span>
                                    {% if membership.is_primary %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Primary
                                        </span>
                                    {% endif %}
                                </div>
                                <a href="{% url 'member:member_teams' member.id %}" class="text-sm text-gray-500 hover:text-gray-700">
                                    View All Teams
                                </a>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-gray-500">No team memberships</div>
                    {% endif %}
                {% endwith %}
                <div class="mt-3">
                    <a href="{% url 'member:member_teams' member.id %}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        Manage Team Memberships
                    </a>
                </div>
            </dd>
        </div>

        <!-- Settings -->
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Settings</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                        <span class="font-medium">Language:</span>
                        {% if member.lang == 1 %}English
                        {% elif member.lang == 2 %}Spanish
                        {% elif member.lang == 3 %}French
                        {% elif member.lang == 4 %}German
                        {% else %}-{% endif %}
                    </div>
                    <div>
                        <span class="font-medium">2FA Method:</span>
                        {% if member.two_factor_auth_method == 1 %}None
                        {% elif member.two_factor_auth_method == 2 %}SMS
                        {% elif member.two_factor_auth_method == 3 %}Email
                        {% elif member.two_factor_auth_method == 4 %}Authenticator App
                        {% else %}-{% endif %}
                    </div>
                </div>
            </dd>
        </div>

        <!-- Notes -->
        {% if member.notes %}
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Notes</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {{ member.notes|linebreaks }}
            </dd>
        </div>
        {% endif %}

        <!-- System Information -->
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">System Information</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                        <span class="font-medium">Created:</span> {{ member.created|date:"F d, Y H:i" }}
                    </div>
                    <div>
                        <span class="font-medium">Last Modified:</span> {{ member.modified|date:"F d, Y H:i" }}
                    </div>
                    <div>
                        <span class="font-medium">Last Login:</span>
                        {% if member.last_login %}
                            {{ member.last_login|date:"F d, Y H:i" }}
                        {% else %}
                            Never
                        {% endif %}
                    </div>
                    <div>
                        <span class="font-medium">Is Active:</span>
                        {% if member.is_active %}Yes{% else %}No{% endif %}
                    </div>
                </div>
            </dd>
        </div>
    </div>
</div>
{% endblock %}
