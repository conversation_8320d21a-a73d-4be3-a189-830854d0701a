from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.db.models import Prefetch
from django.utils.html import format_html

from .models import (
    Communication,
    EmailTemplate,
    Member,
    MemberAuthToken,
    MemberHierarchy,
    MemberPrivacyLog,
    MemberTeam,
    MemberTermsLog,
    MemberType,
    MemberW9Upload,
    PageAccess,
    PasswordReset,
    Permission,
    Privacy,
    Region,
    Team,
    TemplateBuilder,
    Terms,
)


@admin.register(Member)
class MemberAdmin(UserAdmin):
    list_display = Member.LISTDISPLAY
    fieldsets = Member.FIELDSETS
    search_fields = ["username", "email", "first_name", "last_name"]
    list_filter = [
        "status",
        "member_type",
        "region",
        "is_active",
        "is_staff",
        "created",
    ]
    readonly_fields = [
        "created",
        "modified",
        "approved_date",
        "denied_date",
        "last_accepted_terms",
        "last_accepted_privacy",
    ]
    ordering = ["email"]
    list_per_page = 50
    list_max_show_all = 200

    def get_queryset(self, request):
        """Optimize queryset to prevent N+1 queries for terms and privacy logs"""
        return (
            super()
            .get_queryset(request)
            .prefetch_related(
                Prefetch(
                    "terms_logs",
                    queryset=MemberTermsLog.objects.select_related("terms").order_by(
                        "-accepted_at"
                    ),
                    to_attr="cached_terms_logs",
                ),
                Prefetch(
                    "privacy_logs",
                    queryset=MemberPrivacyLog.objects.select_related(
                        "privacy"
                    ).order_by("-accepted_at"),
                    to_attr="cached_privacy_logs",
                ),
            )
        )

    # Add custom actions
    actions = ["approve_members", "deny_members"]

    def approve_members(self, request, queryset):
        updated = queryset.update(status=1, approved_by=request.user)
        self.message_user(request, f"{updated} members were approved.")

    approve_members.short_description = "Approve selected members"

    def deny_members(self, request, queryset):
        updated = queryset.update(status=0, denied_by=request.user)
        self.message_user(request, f"{updated} members were denied.")

    deny_members.short_description = "Deny selected members"

    def last_accepted_terms(self, obj):
        """Display the last accepted terms information using cached data"""
        # Use cached data to prevent N+1 queries
        cached_logs = getattr(obj, "cached_terms_logs", [])
        if cached_logs:
            latest_log = cached_logs[0]
        else:
            latest_log = None
        if latest_log:
            status_icon = "✅" if latest_log.terms.current else "⚠️"
            status_text = "Current" if latest_log.terms.current else "Outdated"
            status_color = "#059669" if latest_log.terms.current else "#d97706"

            return format_html(
                '<div style="background: #f8fafc; padding: 12px; border-radius: 6px; border: 1px solid #e2e8f0; margin: 8px 0;">'
                "<div style=\"color: #1e293b; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\">"
                '<div style="font-weight: 600; color: #374151; margin-bottom: 8px;">Terms Acceptance Status</div>'
                '<div style="font-size: 14px; line-height: 1.5;">'
                '<div style="margin-bottom: 4px;"><strong style="color: #374151;">Terms:</strong> <span style="color: #1e293b;">{}</span></div>'
                '<div style="margin-bottom: 4px;"><strong style="color: #374151;">Accepted:</strong> <span style="color: #1e293b;">{}</span></div>'
                '<div style="margin-bottom: 4px;"><strong style="color: #374151;">Status:</strong> <span style="color: {};">{} {}</span></div>'
                "</div>"
                "</div>"
                "</div>",
                latest_log.terms.title,
                latest_log.accepted_at.strftime("%B %d, %Y at %I:%M %p"),
                status_color,
                status_icon,
                status_text,
            )
        else:
            return format_html(
                '<div style="background: #fef2f2; padding: 12px; border-radius: 6px; border: 1px solid #fecaca; margin: 8px 0;">'
                "<div style=\"color: #1e293b; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\">"
                '<div style="font-weight: 600; color: #dc2626; margin-bottom: 8px;">⚠️ No Terms Accepted</div>'
                '<div style="font-size: 14px; line-height: 1.5; color: #7f1d1d;">'
                "This member has not accepted any terms and conditions yet."
                "</div>"
                "</div>"
                "</div>"
            )

    last_accepted_terms.short_description = "Terms Acceptance Status"

    def last_accepted_privacy(self, obj):
        """Display the last accepted privacy policy information using cached data"""
        # Use cached data to prevent N+1 queries
        cached_logs = getattr(obj, "cached_privacy_logs", [])
        if cached_logs:
            latest_log = cached_logs[0]
        else:
            latest_log = None
        if latest_log:
            status_icon = "✅" if latest_log.privacy.current else "⚠️"
            status_text = "Current" if latest_log.privacy.current else "Outdated"
            status_color = "#059669" if latest_log.privacy.current else "#d97706"

            return format_html(
                '<div style="background: #f8fafc; padding: 12px; border-radius: 6px; border: 1px solid #e2e8f0; margin: 8px 0;">'
                "<div style=\"color: #1e293b; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\">"
                '<div style="font-weight: 600; color: #374151; margin-bottom: 8px;">Privacy Policy Acceptance Status</div>'
                '<div style="font-size: 14px; line-height: 1.5;">'
                '<div style="margin-bottom: 4px;"><strong style="color: #374151;">Policy:</strong> <span style="color: #1e293b;">{}</span></div>'
                '<div style="margin-bottom: 4px;"><strong style="color: #374151;">Accepted:</strong> <span style="color: #1e293b;">{}</span></div>'
                '<div style="margin-bottom: 4px;"><strong style="color: #374151;">Status:</strong> <span style="color: {};">{} {}</span></div>'
                "</div>"
                "</div>"
                "</div>",
                latest_log.privacy.title,
                latest_log.accepted_at.strftime("%B %d, %Y at %I:%M %p"),
                status_color,
                status_icon,
                status_text,
            )
        else:
            return format_html(
                '<div style="background: #fef2f2; padding: 12px; border-radius: 6px; border: 1px solid #fecaca; margin: 8px 0;">'
                "<div style=\"color: #1e293b; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\">"
                '<div style="font-weight: 600; color: #dc2626; margin-bottom: 8px;">⚠️ No Privacy Policy Accepted</div>'
                '<div style="font-size: 14px; line-height: 1.5; color: #7f1d1d;">'
                "This member has not accepted any privacy policy yet."
                "</div>"
                "</div>"
                "</div>"
            )

    last_accepted_privacy.short_description = "Privacy Policy Acceptance Status"


@admin.register(Region)
class RegionAdmin(admin.ModelAdmin):
    list_display = Region.LISTDISPLAY
    fieldsets = Region.FIELDSETS
    search_fields = ["title"]
    list_filter = ["can_signup", "created"]
    readonly_fields = ["created", "modified"]


@admin.register(Terms)
class TermsAdmin(admin.ModelAdmin):
    list_display = Terms.LISTDISPLAY
    fieldsets = Terms.FIELDSETS
    search_fields = ["title"]
    list_filter = ["current", "created"]
    readonly_fields = ["created", "modified"]

    def save_model(self, request, obj, form, change):
        # If this terms is set as current, unset all others
        if obj.current:
            Terms.objects.exclude(pk=obj.pk).update(current=False)
        super().save_model(request, obj, form, change)


@admin.register(Privacy)
class PrivacyAdmin(admin.ModelAdmin):
    list_display = Privacy.LISTDISPLAY
    fieldsets = Privacy.FIELDSETS
    search_fields = ["title"]
    list_filter = ["current", "created"]
    readonly_fields = ["created", "modified"]

    def save_model(self, request, obj, form, change):
        # If this privacy is set as current, unset all others
        if obj.current:
            Privacy.objects.exclude(pk=obj.pk).update(current=False)
        super().save_model(request, obj, form, change)


@admin.register(MemberType)
class MemberTypeAdmin(admin.ModelAdmin):
    list_display = MemberType.LISTDISPLAY
    fieldsets = MemberType.FIELDSETS
    search_fields = ["name", "slug", "description"]
    list_filter = [
        "is_active",
        "can_signup",
        "requires_approval",
        "auto_approve",
        "created",
    ]
    readonly_fields = ["created", "modified"]
    ordering = ["order", "name"]

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        # Add JSON field widgets for better editing
        return form


@admin.register(Permission)
class PermissionAdmin(admin.ModelAdmin):
    list_display = Permission.LISTDISPLAY
    fieldsets = Permission.FIELDSETS
    search_fields = ["name", "slug", "description"]
    list_filter = ["is_active", "category", "created"]
    readonly_fields = ["created", "modified"]
    ordering = ["category", "name"]


@admin.register(PageAccess)
class PageAccessAdmin(admin.ModelAdmin):
    list_display = PageAccess.LISTDISPLAY
    fieldsets = PageAccess.FIELDSETS
    search_fields = ["name", "slug", "url_pattern", "description"]
    list_filter = ["is_active", "category", "requires_permission", "created"]
    readonly_fields = ["created", "modified"]
    ordering = ["category", "name"]


@admin.register(MemberAuthToken)
class MemberAuthTokenAdmin(admin.ModelAdmin):
    list_display = ["member", "auth_type", "issued", "expires", "created"]
    search_fields = ["member__email", "member__username", "auth_type"]
    list_filter = ["auth_type", "issued", "expires", "created"]
    readonly_fields = ["token", "issued", "expires", "created", "modified"]
    ordering = ["-issued"]


@admin.register(MemberTermsLog)
class MemberTermsLogAdmin(admin.ModelAdmin):
    list_display = ["member", "terms", "accepted_at", "created"]
    search_fields = ["member__email", "member__username", "terms__title"]
    list_filter = ["accepted_at", "created", "terms__current"]
    readonly_fields = ["accepted_at", "created", "modified"]
    ordering = ["-accepted_at"]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("member", "terms")


@admin.register(MemberPrivacyLog)
class MemberPrivacyLogAdmin(admin.ModelAdmin):
    list_display = ["member", "privacy", "accepted_at", "created"]
    search_fields = ["member__email", "privacy__title"]
    list_filter = ["accepted_at", "created"]
    readonly_fields = ["accepted_at", "created", "modified"]
    ordering = ["-accepted_at"]


@admin.register(MemberW9Upload)
class MemberW9UploadAdmin(admin.ModelAdmin):
    list_display = ["member", "file", "uploaded_at", "created"]
    search_fields = ["member__email", "member__username"]
    list_filter = ["uploaded_at", "created"]
    readonly_fields = ["uploaded_at", "created", "modified"]
    ordering = ["-uploaded_at"]


@admin.register(Communication)
class CommunicationAdmin(admin.ModelAdmin):
    list_display = Communication.LISTDISPLAY
    fieldsets = Communication.FIELDSETS
    search_fields = [
        "subject",
        "title",
        "to_email",
        "from_email",
        "to_member__username",
        "from_member__username",
        "to_member__email",
        "from_member__email",
    ]
    list_filter = ["communication_type", "status", "priority", "category", "created"]
    readonly_fields = [
        "sent_at",
        "delivered_at",
        "read_at",
        "failed_at",
        "created",
        "modified",
    ]
    ordering = ["-created"]

    # Add custom actions
    actions = ["mark_as_sent", "mark_as_delivered", "mark_as_read"]

    def mark_as_sent(self, request, queryset):
        updated = queryset.update(status="sent")
        self.message_user(request, f"{updated} communications were marked as sent.")

    mark_as_sent.short_description = "Mark selected communications as sent"

    def mark_as_delivered(self, request, queryset):
        updated = queryset.update(status="delivered")
        self.message_user(
            request, f"{updated} communications were marked as delivered."
        )

    mark_as_delivered.short_description = "Mark selected communications as delivered"

    def mark_as_read(self, request, queryset):
        updated = queryset.update(status="read")
        self.message_user(request, f"{updated} communications were marked as read.")

    mark_as_read.short_description = "Mark selected communications as read"


@admin.register(EmailTemplate)
class EmailTemplateAdmin(admin.ModelAdmin):
    list_display = EmailTemplate.LISTDISPLAY
    fieldsets = EmailTemplate.FIELDSETS
    search_fields = ["name", "subject", "title"]
    list_filter = ["template_type", "category", "is_active", "is_system", "created"]
    readonly_fields = ["created", "modified"]
    ordering = ["name"]

    def save_model(self, request, obj, form, change):
        if not change:  # Only for new templates
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(TemplateBuilder)
class TemplateBuilderAdmin(admin.ModelAdmin):
    list_display = ["template", "component_type", "order", "is_active", "created"]
    search_fields = ["template__name", "component_type"]
    list_filter = ["component_type", "is_active", "created"]
    readonly_fields = ["created", "modified"]
    ordering = ["template", "order"]


@admin.register(PasswordReset)
class PasswordResetAdmin(admin.ModelAdmin):
    list_display = PasswordReset.LISTDISPLAY
    fieldsets = PasswordReset.FIELDSETS
    search_fields = ["member__email", "link"]
    list_filter = ["created"]
    readonly_fields = ["created", "modified"]
    ordering = ["-created"]


@admin.register(MemberHierarchy)
class MemberHierarchyAdmin(admin.ModelAdmin):
    list_display = [
        "member",
        "manager",
        "relationship_type",
        "is_primary",
        "is_active",
        "created",
    ]
    list_filter = ["relationship_type", "is_primary", "created", "modified"]
    search_fields = [
        "member__first_name",
        "member__last_name",
        "manager__first_name",
        "manager__last_name",
    ]
    readonly_fields = ["created", "modified"]
    ordering = ["-created"]

    fieldsets = (
        (
            "Relationship",
            {"fields": ("member", "manager", "relationship_type", "is_primary")},
        ),
        ("Timeline", {"fields": ("start_date", "end_date"), "classes": ("collapse",)}),
        ("Additional Info", {"fields": ("notes",), "classes": ("collapse",)}),
        ("System", {"fields": ("created", "modified"), "classes": ("collapse",)}),
    )


@admin.register(Team)
class TeamAdmin(admin.ModelAdmin):
    list_display = [
        "name",
        "team_type",
        "team_lead",
        "get_member_count",
        "is_active",
        "created",
    ]
    list_filter = ["team_type", "is_active", "created", "modified"]
    search_fields = [
        "name",
        "description",
        "team_lead__first_name",
        "team_lead__last_name",
    ]
    readonly_fields = ["created", "modified"]
    ordering = ["name"]

    fieldsets = (
        (
            "Team Information",
            {"fields": ("name", "team_type", "description", "is_active")},
        ),
        (
            "Leadership",
            {
                "fields": ("team_lead",),
                "description": "Assign a team leader for this team",
            },
        ),
        ("System", {"fields": ("created", "modified"), "classes": ("collapse",)}),
    )

    def get_member_count(self, obj):
        return obj.get_member_count()

    get_member_count.short_description = "Members"


@admin.register(MemberTeam)
class MemberTeamAdmin(admin.ModelAdmin):
    list_display = ["member", "team", "role", "is_primary", "is_active", "created"]
    list_filter = ["role", "is_primary", "created", "modified"]
    search_fields = ["member__first_name", "member__last_name", "team__name"]
    readonly_fields = ["created", "modified"]
    ordering = ["-created"]

    fieldsets = (
        ("Membership", {"fields": ("member", "team", "role", "is_primary")}),
        ("Timeline", {"fields": ("start_date", "end_date"), "classes": ("collapse",)}),
        ("Additional Info", {"fields": ("notes",), "classes": ("collapse",)}),
        ("System", {"fields": ("created", "modified"), "classes": ("collapse",)}),
    )
