# api/urls.py
from django.urls import path

from .health import health_check, liveness_check, readiness_check
from .views import (
    ClerkAuthAPI,
    ClerkUserSyncAPI,
    ClerkWebhookAPI,
    CommunicationDetailAPI,
    CommunicationsAdminAPI,
    CommunicationsAPI,
    HelloAPI,
    MemberDetailsAPI,
    MemberHierarchyAPI,
    MemberHierarchyBulkAPI,
    MemberHierarchyDetailAPI,
    MemberHierarchyManagementAPI,
    MemberHierarchySearchAPI,
    MemberProfileAPI,
    MemberProfileDetailAPI,
    MemberProfileSearchAPI,
    MembersAPI,
    MemberSearchAPI,
    MemberTeamsAPI,
    MemberTypeAPI,
    MemberTypeDetailAPI,
    OrganizationChartAPI,
    PasswordResetConfirmAPI,
    PasswordResetHistoryAPI,
    PasswordResetRequestAPI,
    PasswordResetStatsAPI,
    PasswordResetValidateAPI,
    PrivacyAcceptanceAPI,
    PrivacyAPI,
    PrivacyHistoryAPI,
    TeamHierarchyAPI,
    TeamMembersAPI,
    TeamsAPI,
    TermsAcceptanceAPI,
    TermsAPI,
    TermsHistoryAPI,
)

urlpatterns = [
    # Health Check Endpoints
    path("health/", health_check, name="health-check"),
    path("health/readiness/", readiness_check, name="readiness-check"),
    path("health/liveness/", liveness_check, name="liveness-check"),
    # API Endpoints
    path("hello/", HelloAPI.as_view(), name="hello-api"),
    path("member/details/", MemberDetailsAPI.as_view(), name="member-details-api"),
    path("members/", MembersAPI.as_view(), name="members-api"),
    path("members/search/", MemberSearchAPI.as_view(), name="member-search-api"),
    path(
        "organization-chart/",
        OrganizationChartAPI.as_view(),
        name="organization-chart-api",
    ),
    path("teams/", TeamsAPI.as_view(), name="teams-api"),
    path(
        "teams/<int:team_id>/hierarchy/",
        TeamHierarchyAPI.as_view(),
        name="team-hierarchy-api",
    ),
    path(
        "teams/<int:team_id>/members/",
        TeamMembersAPI.as_view(),
        name="team-members-api",
    ),
    path(
        "member/<int:member_id>/hierarchy/",
        MemberHierarchyAPI.as_view(),
        name="member-hierarchy-api",
    ),
    path(
        "member/<int:member_id>/teams/",
        MemberTeamsAPI.as_view(),
        name="member-teams-api",
    ),
    path("member-types/", MemberTypeAPI.as_view(), name="member-types-api"),
    path(
        "member-types/<int:member_type_id>/",
        MemberTypeDetailAPI.as_view(),
        name="member-type-detail-api",
    ),
    # Terms and Conditions API
    path("terms/", TermsAPI.as_view(), name="terms-api"),
    path("terms/accept/", TermsAcceptanceAPI.as_view(), name="terms-acceptance-api"),
    path("terms/history/", TermsHistoryAPI.as_view(), name="terms-history-api"),
    # Privacy Policy API
    path("privacy/", PrivacyAPI.as_view(), name="privacy-api"),
    path(
        "privacy/accept/", PrivacyAcceptanceAPI.as_view(), name="privacy-acceptance-api"
    ),
    path("privacy/history/", PrivacyHistoryAPI.as_view(), name="privacy-history-api"),
    # Communications API
    path("communications/", CommunicationsAPI.as_view(), name="communications-api"),
    path(
        "communications/<int:communication_id>/",
        CommunicationDetailAPI.as_view(),
        name="communication-detail-api",
    ),
    path(
        "communications/admin/",
        CommunicationsAdminAPI.as_view(),
        name="communications-admin-api",
    ),
    # Password Reset API
    path(
        "password-reset/request/",
        PasswordResetRequestAPI.as_view(),
        name="password-reset-request-api",
    ),
    path(
        "password-reset/validate/",
        PasswordResetValidateAPI.as_view(),
        name="password-reset-validate-api",
    ),
    path(
        "password-reset/confirm/",
        PasswordResetConfirmAPI.as_view(),
        name="password-reset-confirm-api",
    ),
    path(
        "password-reset/history/",
        PasswordResetHistoryAPI.as_view(),
        name="password-reset-history-api",
    ),
    path(
        "password-reset/history/<int:member_id>/",
        PasswordResetHistoryAPI.as_view(),
        name="password-reset-history-member-api",
    ),
    path(
        "password-reset/stats/",
        PasswordResetStatsAPI.as_view(),
        name="password-reset-stats-api",
    ),
    # Member Profile Management API
    path("member/profile/", MemberProfileAPI.as_view(), name="member-profile-api"),
    path(
        "member/profile/<int:member_id>/",
        MemberProfileDetailAPI.as_view(),
        name="member-profile-detail-api",
    ),
    path(
        "member/profile/search/",
        MemberProfileSearchAPI.as_view(),
        name="member-profile-search-api",
    ),
    # Member Hierarchy Management API
    path(
        "member/<int:member_id>/hierarchy-management/",
        MemberHierarchyManagementAPI.as_view(),
        name="member-hierarchy-management-api",
    ),
    path(
        "hierarchy/<int:hierarchy_id>/",
        MemberHierarchyDetailAPI.as_view(),
        name="member-hierarchy-detail-api",
    ),
    path(
        "hierarchy/bulk/",
        MemberHierarchyBulkAPI.as_view(),
        name="member-hierarchy-bulk-api",
    ),
    path(
        "hierarchy/search/",
        MemberHierarchySearchAPI.as_view(),
        name="member-hierarchy-search-api",
    ),
    # Clerk Authentication API
    path("clerk/auth/", ClerkAuthAPI.as_view(), name="clerk-auth-api"),
    path("clerk/sync/", ClerkUserSyncAPI.as_view(), name="clerk-sync-api"),
    path("clerk/webhook/", ClerkWebhookAPI.as_view(), name="clerk-webhook-api"),
]
