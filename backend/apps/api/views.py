# api/views.py
import logging
from datetime import timedelta

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.core.mail import send_mail
from django.core.paginator import Paginator
from django.db import DatabaseError, models
from django.db.models import Count, Q
from django.template.loader import render_to_string
from django.utils import timezone

from rest_framework import status as drf_status
from rest_framework.generics import get_object_or_404
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

# Configure logger for this module
logger = logging.getLogger(__name__)


def handle_api_exception(
    exception,
    context="API operation",
    status_code=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
):
    """
    Securely handle API exceptions by logging the full error but returning generic messages.

    Args:
        exception: The caught exception
        context: String describing the operation that failed
        status_code: HTTP status code to return

    Returns:
        Response object with generic error message
    """
    logger.exception(f"Unexpected error in {context}: {str(exception)}")
    return Response(
        {"error": "An unexpected error occurred. Please try again later."},
        status=status_code,
    )


from apps.member.models import (
    Communication,
    Member,
    MemberHierarchy,
    MemberType,
    PasswordReset,
    Privacy,
    Region,
    Team,
    Terms,
)

User = get_user_model()


class HelloAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        return Response({"message": "Hello, JWT-protected world!"})


class MemberDetailsAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get current member details"""
        try:
            member = request.user
            return Response(
                {
                    "id": member.id,
                    "username": member.username,
                    "email": member.email,
                    "first_name": member.first_name,
                    "last_name": member.last_name,
                    "full_name": member.get_full_name(),
                    "member_type": (
                        {
                            "id": member.member_type.id,
                            "name": member.member_type.name,
                            "slug": member.member_type.slug,
                        }
                        if member.member_type
                        else None
                    ),
                    "region": (
                        {
                            "id": member.region.id,
                            "title": member.region.title,
                        }
                        if member.region
                        else None
                    ),
                    "teams": [
                        {
                            "id": membership.team.id,
                            "name": membership.team.name,
                            "role": membership.role,
                            "is_primary": membership.is_primary,
                        }
                        for membership in member.memberteam_set.all()
                    ],
                    "managers": [
                        {
                            "id": hierarchy.manager.id,
                            "name": hierarchy.manager.get_full_name(),
                            "relationship_type": hierarchy.relationship_type,
                            "is_primary": hierarchy.is_primary,
                        }
                        for hierarchy in member.memberhierarchy_set.filter(
                            is_primary=True
                        )
                    ],
                    "subordinates": [
                        {
                            "id": hierarchy.member.id,
                            "name": hierarchy.member.get_full_name(),
                            "relationship_type": hierarchy.relationship_type,
                            "is_primary": hierarchy.is_primary,
                        }
                        for hierarchy in member.memberhierarchy_set.filter(
                            is_primary=True
                        )
                    ],
                    "permissions": member.get_member_type_features().get(
                        "permissions", []
                    ),
                    "features": member.get_member_type_features().get("features", []),
                    "status": member.status,
                    "created": member.created,
                    "modified": member.modified,
                }
            )
        except Exception as e:
            return handle_api_exception(e, "API operation")


class TeamsAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get list of all teams with member counts"""
        try:
            # Get all active teams with member counts
            teams = (
                Team.objects.filter(is_active=True)
                .annotate(member_count=Count("member_teams"))
                .values(
                    "id",
                    "name",
                    "team_type",
                    "description",
                    "member_count",
                    "created",
                    "modified",
                )
            )

            return Response(
                {
                    "teams": list(teams),
                    "total_teams": len(teams),
                    "active_teams": len([t for t in teams if t["member_count"] > 0]),
                }
            )
        except Exception as e:
            return handle_api_exception(e, "API operation")


class MemberHierarchyAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, member_id):
        """Return the full hierarchy tree for a specific member"""
        try:
            member = get_object_or_404(Member, id=member_id)
            max_depth = int(request.GET.get("max_depth", 6))

            def serialize_tree(tree_node, depth=0):
                if not tree_node:
                    return None

                member_obj = tree_node["member"]

                return {
                    "id": member_obj.id,
                    "name": member_obj.get_full_name(),
                    "email": member_obj.email,
                    "member_type": (
                        member_obj.member_type.name if member_obj.member_type else None
                    ),
                    "status": member_obj.status,
                    "depth": depth,
                    "children": (
                        [
                            serialize_tree(child, depth + 1)
                            for child in tree_node.get("subordinates", [])
                        ]
                        if depth < max_depth
                        else []
                    ),
                }

            hierarchy_tree = member.get_hierarchy_tree(max_depth=max_depth)
            serialized_tree = serialize_tree(hierarchy_tree)

            return Response(
                {
                    "member_id": member_id,
                    "member_name": member.get_full_name(),
                    "max_depth": max_depth,
                    "hierarchy": serialized_tree,
                }
            )
        except Exception as e:
            return handle_api_exception(e, "API operation")


class MemberTypeAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get list of all member types with their configurations"""
        try:
            member_types = MemberType.objects.filter(is_active=True).values(
                "id",
                "name",
                "slug",
                "description",
                "permissions",
                "page_access",
                "navigation",
                "feature_flags",
                "dashboard_layout",
                "theme_settings",
                "can_signup",
                "requires_approval",
                "auto_approve",
                "max_subordinates",
                "order",
                "created",
                "modified",
            )

            return Response(
                {
                    "member_types": list(member_types),
                    "total_types": len(member_types),
                    "active_types": len(
                        [mt for mt in member_types if mt["can_signup"]]
                    ),
                }
            )
        except Exception as e:
            return handle_api_exception(e, "API operation")


class MemberTypeDetailAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, member_type_id):
        """Get detailed configuration for a specific member type"""
        try:
            member_type = get_object_or_404(MemberType, id=member_type_id)

            return Response(
                {
                    "id": member_type.id,
                    "name": member_type.name,
                    "slug": member_type.slug,
                    "description": member_type.description,
                    "permissions": member_type.permissions,
                    "page_access": member_type.page_access,
                    "navigation": member_type.navigation,
                    "feature_flags": member_type.feature_flags,
                    "dashboard_layout": member_type.dashboard_layout,
                    "theme_settings": member_type.theme_settings,
                    "can_signup": member_type.can_signup,
                    "requires_approval": member_type.requires_approval,
                    "auto_approve": member_type.auto_approve,
                    "max_subordinates": member_type.max_subordinates,
                    "order": member_type.order,
                    "is_active": member_type.is_active,
                    "created": member_type.created,
                    "modified": member_type.modified,
                    "member_count": member_type.members.count(),
                    "can_manage_types": [
                        {"id": mt.id, "name": mt.name, "slug": mt.slug}
                        for mt in member_type.can_manage_types.all()
                    ],
                }
            )
        except Exception as e:
            return handle_api_exception(e, "API operation")


class MembersAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get paginated list of all members (admin only)"""
        try:
            # Check if user has admin access permission
            if not request.user.has_permission("admin.access"):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            # Get query parameters
            search = request.GET.get("search", "")
            member_type = request.GET.get("member_type", "")
            region = request.GET.get("region", "")
            status_filter = request.GET.get("status", "")
            page = int(request.GET.get("page", 1))
            limit = int(request.GET.get("limit", 20))

            # Start with all members
            queryset = Member.objects.select_related("member_type", "region")

            # Apply filters
            if search:
                queryset = queryset.filter(
                    Q(first_name__icontains=search)
                    | Q(last_name__icontains=search)
                    | Q(email__icontains=search)
                    | Q(username__icontains=search)
                )

            if member_type:
                queryset = queryset.filter(member_type__slug=member_type)

            if region:
                queryset = queryset.filter(region__id=region)

            if status_filter:
                queryset = queryset.filter(status=status_filter)

            # Order by name
            queryset = queryset.order_by("first_name", "last_name", "email")

            # Pagination
            paginator = Paginator(queryset, limit)
            page_obj = paginator.get_page(page)

            # Serialize members
            members_data = []
            for member in page_obj:
                member_data = {
                    "id": member.id,
                    "username": member.username,
                    "email": member.email,
                    "first_name": member.first_name,
                    "last_name": member.last_name,
                    "full_name": member.get_full_name(),
                    "status": member.status,
                    "member_type": (
                        {
                            "id": member.member_type.id,
                            "name": member.member_type.name,
                            "slug": member.member_type.slug,
                        }
                        if member.member_type
                        else None
                    ),
                    "region": (
                        {
                            "id": member.region.id,
                            "title": member.region.title,
                        }
                        if member.region
                        else None
                    ),
                    "phone_cell": member.phone_cell,
                    "phone_work": member.phone_work,
                    "phone_home": member.phone_home,
                    "created": member.created,
                    "modified": member.modified,
                    "last_login": member.last_login,
                    "is_active": member.is_active,
                }
                members_data.append(member_data)

            return Response(
                {
                    "members": members_data,
                    "pagination": {
                        "page": page,
                        "limit": limit,
                        "total_count": paginator.count,
                        "total_pages": paginator.num_pages,
                        "has_next": page_obj.has_next(),
                        "has_previous": page_obj.has_previous(),
                        "next_page": (
                            page_obj.next_page_number() if page_obj.has_next() else None
                        ),
                        "previous_page": (
                            page_obj.previous_page_number()
                            if page_obj.has_previous()
                            else None
                        ),
                    },
                    "filters": {
                        "search": search,
                        "member_type": member_type,
                        "region": region,
                        "status": status_filter,
                    },
                }
            )

        except Exception as e:
            return handle_api_exception(e, "API operation")


class MemberSearchAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Search for specific members with advanced filtering (admin only)"""
        try:
            # Check if user has admin access permission
            if not request.user.has_permission("admin.access"):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            # Get search parameters
            q = request.GET.get("q", "")
            fields = request.GET.get("fields", "name,email,username").split(",")
            fuzzy = request.GET.get("fuzzy", "true").lower() == "true"
            member_type = request.GET.get("member_type", "")
            region = request.GET.get("region", "")
            status_filter = request.GET.get("status", "")
            active_only = request.GET.get("active_only", "true").lower() == "true"
            limit = int(request.GET.get("limit", 10))

            # Start with all members
            queryset = Member.objects.select_related("member_type", "region")

            # Apply active filter if requested
            if active_only:
                queryset = queryset.filter(is_active=True)

            # Build search query
            if q:
                search_filters = Q()

                # Add field-specific searches
                if "name" in fields:
                    search_filters |= Q(first_name__icontains=q) | Q(
                        last_name__icontains=q
                    )
                if "email" in fields:
                    search_filters |= Q(email__icontains=q)
                if "username" in fields:
                    search_filters |= Q(username__icontains=q)
                if "phone" in fields:
                    search_filters |= (
                        Q(phone_cell__icontains=q)
                        | Q(phone_work__icontains=q)
                        | Q(phone_home__icontains=q)
                    )

                # If fuzzy search is enabled, also search in full name
                if fuzzy and "name" in fields:
                    search_filters |= Q(first_name__icontains=q) | Q(
                        last_name__icontains=q
                    )

                queryset = queryset.filter(search_filters)

            # Apply additional filters
            if member_type:
                queryset = queryset.filter(member_type__slug=member_type)

            if region:
                queryset = queryset.filter(region__id=region)

            if status_filter:
                queryset = queryset.filter(status=status_filter)

            # Order by relevance (exact matches first, then partial matches)
            if q:
                # Order by exact matches first
                queryset = queryset.extra(
                    select={
                        "exact_match": "CASE WHEN username = %s OR email = %s THEN 1 ELSE 0 END"
                    },
                    select_params=[q, q],
                ).order_by("-exact_match", "first_name", "last_name", "email")
            else:
                queryset = queryset.order_by("first_name", "last_name", "email")

            # Limit results
            queryset = queryset[:limit]

            # Serialize results with relevance scoring
            members_data = []
            for member in queryset:
                # Calculate relevance score
                relevance_score = 0
                if q:
                    if member.username.lower() == q.lower():
                        relevance_score = 100
                    elif member.email.lower() == q.lower():
                        relevance_score = 95
                    elif member.username.lower().startswith(q.lower()):
                        relevance_score = 90
                    elif member.email.lower().startswith(q.lower()):
                        relevance_score = 85
                    elif q.lower() in member.get_full_name().lower():
                        relevance_score = 80
                    elif q.lower() in member.email.lower():
                        relevance_score = 75
                    else:
                        relevance_score = 50

                member_data = {
                    "id": member.id,
                    "username": member.username,
                    "email": member.email,
                    "first_name": member.first_name,
                    "last_name": member.last_name,
                    "full_name": member.get_full_name(),
                    "status": member.status,
                    "member_type": (
                        {
                            "id": member.member_type.id,
                            "name": member.member_type.name,
                            "slug": member.member_type.slug,
                        }
                        if member.member_type
                        else None
                    ),
                    "region": (
                        {
                            "id": member.region.id,
                            "title": member.region.title,
                        }
                        if member.region
                        else None
                    ),
                    "phone_cell": member.phone_cell,
                    "phone_work": member.phone_work,
                    "phone_home": member.phone_home,
                    "created": member.created,
                    "modified": member.modified,
                    "last_login": member.last_login,
                    "is_active": member.is_active,
                    "relevance_score": relevance_score,
                }
                members_data.append(member_data)

            # Sort by relevance score (highest first)
            if q:
                members_data.sort(key=lambda x: x["relevance_score"], reverse=True)

            return Response(
                {
                    "query": q,
                    "fields_searched": fields,
                    "fuzzy_search": fuzzy,
                    "results": members_data,
                    "total_results": len(members_data),
                    "filters": {
                        "member_type": member_type,
                        "region": region,
                        "status": status_filter,
                        "active_only": active_only,
                    },
                }
            )

        except Exception as e:
            return handle_api_exception(e, "API operation")


class OrganizationChartAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get complete organizational structure (admin only)"""
        try:
            # Check if user has admin access permission
            if not request.user.has_permission("admin.access"):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            # Get query parameters
            depth = int(request.GET.get("depth", 6))
            include_inactive = (
                request.GET.get("include_inactive", "false").lower() == "true"
            )
            member_type_filter = request.GET.get("member_type", "")
            region_filter = request.GET.get("region", "")

            # Start with all active members
            queryset = Member.objects.select_related("member_type", "region")
            if not include_inactive:
                queryset = queryset.filter(is_active=True)

            # Apply filters
            if member_type_filter:
                queryset = queryset.filter(member_type__slug=member_type_filter)

            if region_filter:
                queryset = queryset.filter(region__id=region_filter)

            # Find top-level members (those without primary managers)
            top_level_members = []
            for member in queryset:
                primary_manager = member.get_primary_manager()
                if not primary_manager:
                    top_level_members.append(member)

            def serialize_member(member_obj):
                """Serialize a member with hierarchy information"""
                return {
                    "id": member_obj.id,
                    "username": member_obj.username,
                    "email": member_obj.email,
                    "first_name": member_obj.first_name,
                    "last_name": member_obj.last_name,
                    "full_name": member_obj.get_full_name(),
                    "status": member_obj.status,
                    "member_type": (
                        {
                            "id": member_obj.member_type.id,
                            "name": member_obj.member_type.name,
                            "slug": member_obj.member_type.slug,
                        }
                        if member_obj.member_type
                        else None
                    ),
                    "region": (
                        {
                            "id": member_obj.region.id,
                            "title": member_obj.region.title,
                        }
                        if member_obj.region
                        else None
                    ),
                    "phone_cell": member_obj.phone_cell,
                    "phone_work": member_obj.phone_work,
                    "phone_home": member_obj.phone_home,
                    "created": member_obj.created,
                    "modified": member_obj.modified,
                    "last_login": member_obj.last_login,
                    "is_active": member_obj.is_active,
                }

            def build_org_tree(member, current_depth=0):
                """Recursively build organizational tree"""
                if current_depth >= depth:
                    return None

                # Get direct subordinates
                subordinates = member.get_direct_subordinates(
                    active_only=not include_inactive
                )

                # Filter subordinates based on member_type and region if specified
                if member_type_filter:
                    subordinates = subordinates.filter(
                        member__member_type__slug=member_type_filter
                    )
                if region_filter:
                    subordinates = subordinates.filter(member__region__id=region_filter)

                tree_node = {
                    "member": serialize_member(member),
                    "depth": current_depth,
                    "subordinates": [],
                    "subordinate_count": subordinates.count(),
                    "all_managers": [
                        {
                            "id": hierarchy.manager.id,
                            "name": hierarchy.manager.get_full_name(),
                            "relationship_type": hierarchy.relationship_type,
                            "is_primary": hierarchy.is_primary,
                        }
                        for hierarchy in member.get_all_managers(
                            active_only=not include_inactive
                        )
                    ],
                    "all_subordinates": [
                        {
                            "id": hierarchy.member.id,
                            "name": hierarchy.member.get_full_name(),
                            "relationship_type": hierarchy.relationship_type,
                            "is_primary": hierarchy.is_primary,
                        }
                        for hierarchy in member.get_subordinates(
                            active_only=not include_inactive
                        )
                    ],
                }

                # Recursively build subordinate trees
                for sub_hierarchy in subordinates:
                    sub_tree = build_org_tree(sub_hierarchy.member, current_depth + 1)
                    if sub_tree:
                        tree_node["subordinates"].append(sub_tree)

                return tree_node

            # Build organizational trees for all top-level members
            org_trees = []
            for top_member in top_level_members:
                tree = build_org_tree(top_member, 0)
                if tree:
                    org_trees.append(tree)

            # Calculate organization statistics
            total_members = queryset.count()
            active_members = queryset.filter(is_active=True).count()
            inactive_members = total_members - active_members

            # Count members by type
            member_type_counts = {}
            for member_type in MemberType.objects.all():
                count = queryset.filter(member_type=member_type).count()
                if count > 0:
                    member_type_counts[member_type.name] = count

            # Count members by region
            region_counts = {}
            for region in Region.objects.all():
                count = queryset.filter(region=region).count()
                if count > 0:
                    region_counts[region.title] = count

            # Find orphaned members (those without any managers)
            orphaned_members = []
            for member in queryset:
                if not member.get_all_managers(
                    active_only=not include_inactive
                ).exists():
                    orphaned_members.append(serialize_member(member))

            return Response(
                {
                    "organization_chart": {
                        "top_level_members": org_trees,
                        "orphaned_members": orphaned_members,
                        "total_trees": len(org_trees),
                        "max_depth": depth,
                    },
                    "statistics": {
                        "total_members": total_members,
                        "active_members": active_members,
                        "inactive_members": inactive_members,
                        "top_level_count": len(top_level_members),
                        "orphaned_count": len(orphaned_members),
                        "member_type_distribution": member_type_counts,
                        "region_distribution": region_counts,
                    },
                    "filters": {
                        "depth": depth,
                        "include_inactive": include_inactive,
                        "member_type": member_type_filter,
                        "region": region_filter,
                    },
                }
            )

        except Exception as e:
            return handle_api_exception(e, "API operation")


class TeamHierarchyAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, team_id):
        """Get hierarchical structure within a specific team (admin only)"""
        try:
            # Check if user has admin access permission
            if not request.user.has_permission("admin.access"):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            # Get the team
            team = get_object_or_404(Team, id=team_id)

            # Get query parameters
            depth = int(request.GET.get("depth", 6))
            include_inactive = (
                request.GET.get("include_inactive", "false").lower() == "true"
            )
            role_filter = request.GET.get("role", "")
            active_only = request.GET.get("active_only", "true").lower() == "true"

            # Get team memberships
            memberships = team.get_memberships(active_only=not include_inactive)

            # Apply role filter if specified
            if role_filter:
                memberships = memberships.filter(role=role_filter)

            # Get team members
            team_members = [membership.member for membership in memberships]

            def serialize_member(member_obj, membership_obj=None):
                """Serialize a member with team membership information"""
                member_data = {
                    "id": member_obj.id,
                    "username": member_obj.username,
                    "email": member_obj.email,
                    "first_name": member_obj.first_name,
                    "last_name": member_obj.last_name,
                    "full_name": member_obj.get_full_name(),
                    "status": member_obj.status,
                    "member_type": (
                        {
                            "id": member_obj.member_type.id,
                            "name": member_obj.member_type.name,
                            "slug": member_obj.member_type.slug,
                        }
                        if member_obj.member_type
                        else None
                    ),
                    "region": (
                        {
                            "id": member_obj.region.id,
                            "title": member_obj.region.title,
                        }
                        if member_obj.region
                        else None
                    ),
                    "phone_cell": member_obj.phone_cell,
                    "phone_work": member_obj.phone_work,
                    "phone_home": member_obj.phone_home,
                    "created": member_obj.created,
                    "modified": member_obj.modified,
                    "last_login": member_obj.last_login,
                    "is_active": member_obj.is_active,
                }

                # Add team-specific information if membership is provided
                if membership_obj:
                    member_data["team_membership"] = {
                        "role": membership_obj.role,
                        "is_primary": membership_obj.is_primary,
                        "start_date": membership_obj.start_date,
                        "end_date": membership_obj.end_date,
                        "notes": membership_obj.notes,
                        "created": membership_obj.created,
                        "modified": membership_obj.modified,
                    }

                return member_data

            def build_team_hierarchy(member, current_depth=0, visited=None):
                """Recursively build team hierarchy based on organizational structure"""
                if visited is None:
                    visited = set()

                if current_depth >= depth or member.id in visited:
                    return None

                visited.add(member.id)

                # Get team membership for this member
                membership = member.team_memberships.filter(team=team).first()

                # Get subordinates who are also in this team
                subordinates = member.get_direct_subordinates(
                    active_only=not include_inactive
                )
                team_subordinates = []

                for sub_hierarchy in subordinates:
                    sub_member = sub_hierarchy.member
                    # Check if subordinate is also in this team
                    sub_membership = sub_member.team_memberships.filter(
                        team=team
                    ).first()
                    if sub_membership:
                        if not role_filter or sub_membership.role == role_filter:
                            team_subordinates.append(sub_hierarchy)

                # Build hierarchy node
                hierarchy_node = {
                    "member": serialize_member(member, membership),
                    "depth": current_depth,
                    "subordinates": [],
                    "subordinate_count": len(team_subordinates),
                    "is_team_lead": member == team.team_lead,
                    "all_managers": [
                        {
                            "id": hierarchy.manager.id,
                            "name": hierarchy.manager.get_full_name(),
                            "relationship_type": hierarchy.relationship_type,
                            "is_primary": hierarchy.is_primary,
                            "in_team": hierarchy.manager.team_memberships.filter(
                                team=team
                            ).exists(),
                        }
                        for hierarchy in member.get_all_managers(
                            active_only=not include_inactive
                        )
                    ],
                    "all_subordinates": [
                        {
                            "id": hierarchy.member.id,
                            "name": hierarchy.member.get_full_name(),
                            "relationship_type": hierarchy.relationship_type,
                            "is_primary": hierarchy.is_primary,
                            "in_team": hierarchy.member.team_memberships.filter(
                                team=team
                            ).exists(),
                        }
                        for hierarchy in member.get_subordinates(
                            active_only=not include_inactive
                        )
                    ],
                }

                # Recursively build subordinate trees
                for sub_hierarchy in team_subordinates:
                    sub_tree = build_team_hierarchy(
                        sub_hierarchy.member, current_depth + 1, visited
                    )
                    if sub_tree:
                        hierarchy_node["subordinates"].append(sub_tree)

                return hierarchy_node

            # Find top-level members in the team (those without managers in the team)
            top_level_members = []
            for member in team_members:
                # Check if this member has any managers who are also in the team
                managers_in_team = member.get_all_managers(
                    active_only=not include_inactive
                ).filter(manager__team_memberships__team=team)
                if not managers_in_team.exists():
                    top_level_members.append(member)

            # If no top-level members found, use team lead or first member
            if not top_level_members:
                if team.team_lead and team.team_lead in team_members:
                    top_level_members = [team.team_lead]
                elif team_members:
                    top_level_members = [team_members[0]]

            # Build team hierarchy trees
            team_hierarchy_trees = []
            for top_member in top_level_members:
                tree = build_team_hierarchy(top_member, 0)
                if tree:
                    team_hierarchy_trees.append(tree)

            # Calculate team statistics
            total_members = len(team_members)
            active_members = len([m for m in team_members if m.is_active])
            inactive_members = total_members - active_members

            # Count members by role
            role_counts = {}
            for membership in memberships:
                role = membership.role
                role_counts[role] = role_counts.get(role, 0) + 1

            # Count members by member type
            member_type_counts = {}
            for member in team_members:
                if member.member_type:
                    member_type_name = member.member_type.name
                    member_type_counts[member_type_name] = (
                        member_type_counts.get(member_type_name, 0) + 1
                    )

            # Count members by region
            region_counts = {}
            for member in team_members:
                if member.region:
                    region_name = member.region.title
                    region_counts[region_name] = region_counts.get(region_name, 0) + 1

            # Find orphaned members in team (those without managers in the team)
            orphaned_members = []
            for member in team_members:
                managers_in_team = member.get_all_managers(
                    active_only=not include_inactive
                ).filter(manager__team_memberships__team=team)
                if not managers_in_team.exists():
                    membership = member.team_memberships.filter(team=team).first()
                    orphaned_members.append(serialize_member(member, membership))

            return Response(
                {
                    "team": {
                        "id": team.id,
                        "name": team.name,
                        "team_type": team.team_type,
                        "description": team.description,
                        "team_lead": (
                            serialize_member(team.team_lead) if team.team_lead else None
                        ),
                        "is_active": team.is_active,
                        "created": team.created,
                        "modified": team.modified,
                    },
                    "team_hierarchy": {
                        "top_level_members": team_hierarchy_trees,
                        "orphaned_members": orphaned_members,
                        "total_trees": len(team_hierarchy_trees),
                        "max_depth": depth,
                    },
                    "statistics": {
                        "total_members": total_members,
                        "active_members": active_members,
                        "inactive_members": inactive_members,
                        "top_level_count": len(top_level_members),
                        "orphaned_count": len(orphaned_members),
                        "role_distribution": role_counts,
                        "member_type_distribution": member_type_counts,
                        "region_distribution": region_counts,
                    },
                    "filters": {
                        "depth": depth,
                        "include_inactive": include_inactive,
                        "role": role_filter,
                        "active_only": active_only,
                    },
                }
            )

        except Exception as e:
            return handle_api_exception(e, "API operation")


class MemberTeamsAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, member_id):
        """Get all teams a specific member belongs to (admin only)"""
        try:
            # Check if user has admin access permission
            if not request.user.has_permission("admin.access"):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            # Get the member
            member = get_object_or_404(Member, id=member_id)

            # Get query parameters
            role_filter = request.GET.get("role", "")
            active_only = request.GET.get("active_only", "true").lower() == "true"
            include_inactive = (
                request.GET.get("include_inactive", "false").lower() == "true"
            )
            primary_only = request.GET.get("primary_only", "false").lower() == "true"

            # Get team memberships
            memberships = member.get_team_memberships(active_only=not include_inactive)

            # Apply role filter if specified
            if role_filter:
                memberships = memberships.filter(role=role_filter)

            # Apply primary filter if specified
            if primary_only:
                memberships = memberships.filter(is_primary=True)

            def serialize_team_membership(membership_obj):
                """Serialize a team membership with team details"""
                team = membership_obj.team

                # Get team lead information
                team_lead_data = None
                if team.team_lead:
                    team_lead_data = {
                        "id": team.team_lead.id,
                        "username": team.team_lead.username,
                        "email": team.team_lead.email,
                        "first_name": team.team_lead.first_name,
                        "last_name": team.team_lead.last_name,
                        "full_name": team.team_lead.get_full_name(),
                        "member_type": (
                            {
                                "id": team.team_lead.member_type.id,
                                "name": team.team_lead.member_type.name,
                                "slug": team.team_lead.member_type.slug,
                            }
                            if team.team_lead.member_type
                            else None
                        ),
                    }

                # Get team member count
                member_count = team.get_member_count()
                active_member_count = len(team.get_active_members())

                return {
                    "team": {
                        "id": team.id,
                        "name": team.name,
                        "team_type": team.team_type,
                        "description": team.description,
                        "team_lead": team_lead_data,
                        "is_active": team.is_active,
                        "created": team.created,
                        "modified": team.modified,
                        "member_count": member_count,
                        "active_member_count": active_member_count,
                    },
                    "membership": {
                        "role": membership_obj.role,
                        "is_primary": membership_obj.is_primary,
                        "start_date": membership_obj.start_date,
                        "end_date": membership_obj.end_date,
                        "notes": membership_obj.notes,
                        "created": membership_obj.created,
                        "modified": membership_obj.modified,
                        "is_active": membership_obj.is_active,
                    },
                    "member_in_team": {
                        "id": member.id,
                        "username": member.username,
                        "email": member.email,
                        "first_name": member.first_name,
                        "last_name": member.last_name,
                        "full_name": member.get_full_name(),
                        "member_type": (
                            {
                                "id": member.member_type.id,
                                "name": member.member_type.name,
                                "slug": member.member_type.slug,
                            }
                            if member.member_type
                            else None
                        ),
                        "region": (
                            {
                                "id": member.region.id,
                                "title": member.region.title,
                            }
                            if member.region
                            else None
                        ),
                        "status": member.status,
                        "is_active": member.is_active,
                    },
                }

            # Serialize all memberships
            teams_data = [
                serialize_team_membership(membership) for membership in memberships
            ]

            # Calculate statistics
            total_teams = len(teams_data)
            primary_teams = len(
                [t for t in teams_data if t["membership"]["is_primary"]]
            )
            active_teams = len([t for t in teams_data if t["team"]["is_active"]])
            active_memberships = len(
                [t for t in teams_data if t["membership"]["is_active"]]
            )

            # Count teams by role
            role_counts = {}
            for team_data in teams_data:
                role = team_data["membership"]["role"]
                role_counts[role] = role_counts.get(role, 0) + 1

            # Count teams by type
            team_type_counts = {}
            for team_data in teams_data:
                team_type = team_data["team"]["team_type"]
                team_type_counts[team_type] = team_type_counts.get(team_type, 0) + 1

            # Get member's primary team
            primary_team = None
            primary_membership = member.get_primary_team()
            if primary_membership:
                primary_team = serialize_team_membership(primary_membership)

            # Get teams where member is team lead
            teams_as_lead = []
            for team_data in teams_data:
                if (
                    team_data["team"]["team_lead"]
                    and team_data["team"]["team_lead"]["id"] == member.id
                ):
                    teams_as_lead.append(team_data)

            return Response(
                {
                    "member": {
                        "id": member.id,
                        "username": member.username,
                        "email": member.email,
                        "first_name": member.first_name,
                        "last_name": member.last_name,
                        "full_name": member.get_full_name(),
                        "status": member.status,
                        "member_type": (
                            {
                                "id": member.member_type.id,
                                "name": member.member_type.name,
                                "slug": member.member_type.slug,
                            }
                            if member.member_type
                            else None
                        ),
                        "region": (
                            {
                                "id": member.region.id,
                                "title": member.region.title,
                            }
                            if member.region
                            else None
                        ),
                        "phone_cell": member.phone_cell,
                        "phone_work": member.phone_work,
                        "phone_home": member.phone_home,
                        "created": member.created,
                        "modified": member.modified,
                        "last_login": member.last_login,
                        "is_active": member.is_active,
                    },
                    "teams": teams_data,
                    "primary_team": primary_team,
                    "teams_as_lead": teams_as_lead,
                    "statistics": {
                        "total_teams": total_teams,
                        "primary_teams": primary_teams,
                        "active_teams": active_teams,
                        "active_memberships": active_memberships,
                        "teams_as_lead_count": len(teams_as_lead),
                        "role_distribution": role_counts,
                        "team_type_distribution": team_type_counts,
                    },
                    "filters": {
                        "role": role_filter,
                        "active_only": active_only,
                        "include_inactive": include_inactive,
                        "primary_only": primary_only,
                    },
                }
            )

        except Exception as e:
            return handle_api_exception(e, "API operation")


class TeamMembersAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, team_id):
        """Get all members in a specific team (admin only)"""
        try:
            # Check if user has admin access permission
            if not request.user.has_permission("admin.access"):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            # Get the team
            team = get_object_or_404(Team, id=team_id)

            # Get query parameters
            role_filter = request.GET.get("role", "")
            active_only = request.GET.get("active_only", "true").lower() == "true"
            include_inactive = (
                request.GET.get("include_inactive", "false").lower() == "true"
            )
            primary_only = request.GET.get("primary_only", "false").lower() == "true"
            member_type_filter = request.GET.get("member_type", "")
            region_filter = request.GET.get("region", "")
            search = request.GET.get("search", "")

            # Get team memberships
            memberships = team.get_memberships(active_only=not include_inactive)

            # Apply role filter if specified
            if role_filter:
                memberships = memberships.filter(role=role_filter)

            # Apply primary filter if specified
            if primary_only:
                memberships = memberships.filter(is_primary=True)

            # Apply member type filter if specified
            if member_type_filter:
                memberships = memberships.filter(
                    member__member_type__slug=member_type_filter
                )

            # Apply region filter if specified
            if region_filter:
                memberships = memberships.filter(member__region__id=region_filter)

            # Apply search filter if specified
            if search:
                memberships = memberships.filter(
                    Q(member__first_name__icontains=search)
                    | Q(member__last_name__icontains=search)
                    | Q(member__email__icontains=search)
                    | Q(member__username__icontains=search)
                )

            def serialize_team_member(membership_obj):
                """Serialize a team member with membership details"""
                member = membership_obj.member

                # Get member's managers in this team
                managers_in_team = []
                for hierarchy in member.get_all_managers(
                    active_only=not include_inactive
                ):
                    if hierarchy.manager.team_memberships.filter(team=team).exists():
                        managers_in_team.append(
                            {
                                "id": hierarchy.manager.id,
                                "name": hierarchy.manager.get_full_name(),
                                "relationship_type": hierarchy.relationship_type,
                                "is_primary": hierarchy.is_primary,
                            }
                        )

                # Get member's subordinates in this team
                subordinates_in_team = []
                for hierarchy in member.get_subordinates(
                    active_only=not include_inactive
                ):
                    if hierarchy.member.team_memberships.filter(team=team).exists():
                        subordinates_in_team.append(
                            {
                                "id": hierarchy.member.id,
                                "name": hierarchy.member.get_full_name(),
                                "relationship_type": hierarchy.relationship_type,
                                "is_primary": hierarchy.is_primary,
                            }
                        )

                return {
                    "member": {
                        "id": member.id,
                        "username": member.username,
                        "email": member.email,
                        "first_name": member.first_name,
                        "last_name": member.last_name,
                        "full_name": member.get_full_name(),
                        "status": member.status,
                        "member_type": (
                            {
                                "id": member.member_type.id,
                                "name": member.member_type.name,
                                "slug": member.member_type.slug,
                            }
                            if member.member_type
                            else None
                        ),
                        "region": (
                            {
                                "id": member.region.id,
                                "title": member.region.title,
                            }
                            if member.region
                            else None
                        ),
                        "phone_cell": member.phone_cell,
                        "phone_work": member.phone_work,
                        "phone_home": member.phone_home,
                        "created": member.created,
                        "modified": member.modified,
                        "last_login": member.last_login,
                        "is_active": member.is_active,
                    },
                    "membership": {
                        "role": membership_obj.role,
                        "is_primary": membership_obj.is_primary,
                        "start_date": membership_obj.start_date,
                        "end_date": membership_obj.end_date,
                        "notes": membership_obj.notes,
                        "created": membership_obj.created,
                        "modified": membership_obj.modified,
                        "is_active": membership_obj.is_active,
                    },
                    "team_relationships": {
                        "managers_in_team": managers_in_team,
                        "subordinates_in_team": subordinates_in_team,
                        "is_team_lead": member == team.team_lead,
                        "manager_count": len(managers_in_team),
                        "subordinate_count": len(subordinates_in_team),
                    },
                }

            # Serialize all team members
            team_members_data = [
                serialize_team_member(membership) for membership in memberships
            ]

            # Calculate team statistics
            total_members = len(team_members_data)
            active_members = len(
                [m for m in team_members_data if m["member"]["is_active"]]
            )
            inactive_members = total_members - active_members
            primary_members = len(
                [m for m in team_members_data if m["membership"]["is_primary"]]
            )
            active_memberships = len(
                [m for m in team_members_data if m["membership"]["is_active"]]
            )

            # Count members by role
            role_counts = {}
            for member_data in team_members_data:
                role = member_data["membership"]["role"]
                role_counts[role] = role_counts.get(role, 0) + 1

            # Count members by member type
            member_type_counts = {}
            for member_data in team_members_data:
                if member_data["member"]["member_type"]:
                    member_type_name = member_data["member"]["member_type"]["name"]
                    member_type_counts[member_type_name] = (
                        member_type_counts.get(member_type_name, 0) + 1
                    )

            # Count members by region
            region_counts = {}
            for member_data in team_members_data:
                if member_data["member"]["region"]:
                    region_name = member_data["member"]["region"]["title"]
                    region_counts[region_name] = region_counts.get(region_name, 0) + 1

            # Get team lead information
            team_lead_data = None
            if team.team_lead:
                team_lead_data = {
                    "id": team.team_lead.id,
                    "username": team.team_lead.username,
                    "email": team.team_lead.email,
                    "first_name": team.team_lead.first_name,
                    "last_name": team.team_lead.last_name,
                    "full_name": team.team_lead.get_full_name(),
                    "member_type": (
                        {
                            "id": team.team_lead.member_type.id,
                            "name": team.team_lead.member_type.name,
                            "slug": team.team_lead.member_type.slug,
                        }
                        if team.team_lead.member_type
                        else None
                    ),
                    "region": (
                        {
                            "id": team.team_lead.region.id,
                            "title": team.team_lead.region.title,
                        }
                        if team.team_lead.region
                        else None
                    ),
                }

            # Group members by role for easier frontend consumption
            members_by_role = {}
            for member_data in team_members_data:
                role = member_data["membership"]["role"]
                if role not in members_by_role:
                    members_by_role[role] = []
                members_by_role[role].append(member_data)

            return Response(
                {
                    "team": {
                        "id": team.id,
                        "name": team.name,
                        "team_type": team.team_type,
                        "description": team.description,
                        "team_lead": team_lead_data,
                        "is_active": team.is_active,
                        "created": team.created,
                        "modified": team.modified,
                        "member_count": team.get_member_count(),
                        "active_member_count": len(team.get_active_members()),
                    },
                    "members": team_members_data,
                    "members_by_role": members_by_role,
                    "statistics": {
                        "total_members": total_members,
                        "active_members": active_members,
                        "inactive_members": inactive_members,
                        "primary_members": primary_members,
                        "active_memberships": active_memberships,
                        "role_distribution": role_counts,
                        "member_type_distribution": member_type_counts,
                        "region_distribution": region_counts,
                    },
                    "filters": {
                        "role": role_filter,
                        "active_only": active_only,
                        "include_inactive": include_inactive,
                        "primary_only": primary_only,
                        "member_type": member_type_filter,
                        "region": region_filter,
                        "search": search,
                    },
                }
            )

        except Exception as e:
            return handle_api_exception(e, "API operation")


class TermsAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get current terms and conditions"""
        try:
            # Get current terms
            current_terms = Terms.objects.filter(current=True).first()
            if not current_terms:
                # If no current terms, get the most recent one
                current_terms = Terms.objects.order_by("-created").first()

            if not current_terms:
                return Response(
                    {"error": "No terms and conditions available"},
                    status=drf_status.HTTP_404_NOT_FOUND,
                )

            # Check if user has accepted the current terms
            has_accepted = False
            if request.user.is_authenticated:
                has_accepted = request.user.has_accepted_terms(current_terms)

            return Response(
                {
                    "terms": {
                        "id": current_terms.id,
                        "title": current_terms.title,
                        "current": current_terms.current,
                        "body_html": current_terms.body_html,
                        "body_text": current_terms.body_text,
                        "created": current_terms.created,
                        "modified": current_terms.modified,
                    },
                    "user_acceptance": {
                        "has_accepted": has_accepted,
                        "accepted_at": None,
                    },
                }
            )

        except Exception as e:
            return handle_api_exception(e, "API operation")


class TermsAcceptanceAPI(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Accept the current terms and conditions"""
        try:
            # Get current terms
            current_terms = Terms.objects.filter(current=True).first()
            if not current_terms:
                return Response(
                    {"error": "No current terms available to accept"},
                    status=drf_status.HTTP_404_NOT_FOUND,
                )

            # Accept the terms
            log, created = request.user.accept_terms(current_terms)

            return Response(
                {
                    "success": True,
                    "message": f"Successfully accepted terms: {current_terms.title}",
                    "terms": {
                        "id": current_terms.id,
                        "title": current_terms.title,
                        "current": current_terms.current,
                    },
                    "acceptance": {
                        "accepted_at": log.accepted_at,
                        "was_new_acceptance": created,
                        "previously_accepted": not created,
                    },
                }
            )

        except Exception as e:
            return handle_api_exception(e, "API operation")

    def get(self, request):
        """Get user's terms acceptance history"""
        try:
            # Get user's acceptance history
            acceptance_logs = request.user.terms_logs.select_related("terms").order_by(
                "-accepted_at"
            )

            acceptance_history = []
            for log in acceptance_logs:
                acceptance_history.append(
                    {
                        "id": log.id,
                        "terms": {
                            "id": log.terms.id,
                            "title": log.terms.title,
                            "current": log.terms.current,
                        },
                        "accepted_at": log.accepted_at,
                        "created": log.created,
                    }
                )

            # Get current terms status
            current_terms = Terms.objects.filter(current=True).first()
            has_accepted_current = False
            if current_terms:
                has_accepted_current = request.user.has_accepted_terms(current_terms)

            return Response(
                {
                    "current_terms": {
                        "id": current_terms.id if current_terms else None,
                        "title": current_terms.title if current_terms else None,
                        "current": current_terms.current if current_terms else False,
                        "has_accepted": has_accepted_current,
                    },
                    "acceptance_history": acceptance_history,
                    "total_acceptances": len(acceptance_history),
                }
            )

        except Exception as e:
            return handle_api_exception(e, "API operation")


class TermsHistoryAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get all terms versions (admin only)"""
        try:
            # Check if user has admin access permission
            if not request.user.has_permission("admin.access"):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            # Get all terms versions
            terms_list = Terms.objects.all().order_by("-created")

            terms_data = []
            for terms in terms_list:
                # Get acceptance count for this terms version
                acceptance_count = terms.member_logs.count()

                terms_data.append(
                    {
                        "id": terms.id,
                        "title": terms.title,
                        "current": terms.current,
                        "body_html": terms.body_html,
                        "body_text": terms.body_text,
                        "created": terms.created,
                        "modified": terms.modified,
                        "acceptance_count": acceptance_count,
                    }
                )

            return Response(
                {
                    "terms_versions": terms_data,
                    "total_versions": len(terms_data),
                    "current_version": next(
                        (t for t in terms_data if t["current"]), None
                    ),
                }
            )

        except Exception as e:
            return handle_api_exception(e, "API operation")


class PrivacyAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get current privacy policy"""
        try:
            current_privacy = Privacy.objects.filter(current=True).first()
            if not current_privacy:
                current_privacy = Privacy.objects.order_by("-created").first()
            if not current_privacy:
                return Response(
                    {"error": "No privacy policy available"},
                    status=drf_status.HTTP_404_NOT_FOUND,
                )
            has_accepted = False
            if request.user.is_authenticated:
                has_accepted = request.user.has_accepted_privacy(current_privacy)
            return Response(
                {
                    "privacy": {
                        "id": current_privacy.id,
                        "title": current_privacy.title,
                        "current": current_privacy.current,
                        "body_html": current_privacy.body_html,
                        "body_text": current_privacy.body_text,
                        "created": current_privacy.created,
                        "modified": current_privacy.modified,
                    },
                    "user_acceptance": {
                        "has_accepted": has_accepted,
                        "accepted_at": None,
                    },
                }
            )
        except Exception as e:
            return handle_api_exception(e, "API operation")


class PrivacyAcceptanceAPI(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Accept the current privacy policy"""
        try:
            current_privacy = Privacy.objects.filter(current=True).first()
            if not current_privacy:
                return Response(
                    {"error": "No current privacy policy available to accept"},
                    status=drf_status.HTTP_404_NOT_FOUND,
                )
            log, created = request.user.accept_privacy(current_privacy)
            return Response(
                {
                    "success": True,
                    "message": f"Successfully accepted privacy policy: {current_privacy.title}",
                    "privacy": {
                        "id": current_privacy.id,
                        "title": current_privacy.title,
                        "current": current_privacy.current,
                    },
                    "acceptance": {
                        "accepted_at": log.accepted_at,
                        "was_new_acceptance": created,
                        "previously_accepted": not created,
                    },
                }
            )
        except Exception as e:
            return handle_api_exception(e, "API operation")

    def get(self, request):
        """Get user's privacy policy acceptance history"""
        try:
            acceptance_logs = request.user.privacy_logs.select_related(
                "privacy"
            ).order_by("-accepted_at")
            acceptance_history = []
            for log in acceptance_logs:
                acceptance_history.append(
                    {
                        "id": log.id,
                        "privacy": {
                            "id": log.privacy.id,
                            "title": log.privacy.title,
                            "current": log.privacy.current,
                        },
                        "accepted_at": log.accepted_at,
                        "created": log.created,
                    }
                )
            current_privacy = Privacy.objects.filter(current=True).first()
            has_accepted_current = False
            if current_privacy:
                has_accepted_current = request.user.has_accepted_privacy(
                    current_privacy
                )
            return Response(
                {
                    "current_privacy": {
                        "id": current_privacy.id if current_privacy else None,
                        "title": current_privacy.title if current_privacy else None,
                        "current": (
                            current_privacy.current if current_privacy else False
                        ),
                        "has_accepted": has_accepted_current,
                    },
                    "acceptance_history": acceptance_history,
                    "total_acceptances": len(acceptance_history),
                }
            )
        except Exception as e:
            return handle_api_exception(e, "API operation")


class PrivacyHistoryAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get all privacy policy versions (admin only)"""
        try:
            if not request.user.has_permission("admin.access"):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )
            privacy_list = Privacy.objects.all().order_by("-created")
            privacy_data = []
            for privacy in privacy_list:
                acceptance_count = privacy.member_logs.count()
                privacy_data.append(
                    {
                        "id": privacy.id,
                        "title": privacy.title,
                        "current": privacy.current,
                        "body_html": privacy.body_html,
                        "body_text": privacy.body_text,
                        "created": privacy.created,
                        "modified": privacy.modified,
                        "acceptance_count": acceptance_count,
                    }
                )
            return Response(
                {
                    "privacy_versions": privacy_data,
                    "total_versions": len(privacy_data),
                    "current_version": next(
                        (p for p in privacy_data if p["current"]), None
                    ),
                }
            )
        except Exception as e:
            return handle_api_exception(e, "API operation")


class CommunicationsAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get communications for the authenticated user"""
        try:
            # Get query parameters
            communication_type = request.GET.get("type", "")
            status = request.GET.get("status", "")
            priority = request.GET.get("priority", "")
            category = request.GET.get("category", "")
            page = int(request.GET.get("page", 1))
            limit = int(request.GET.get("limit", 20))

            # Start with user's communications
            queryset = Communication.objects.filter(to_member=request.user)

            # Apply filters
            if communication_type:
                queryset = queryset.filter(communication_type=communication_type)
            if status:
                queryset = queryset.filter(status=status)
            if priority:
                queryset = queryset.filter(priority=priority)
            if category:
                queryset = queryset.filter(category=category)

            # Order by created date (newest first)
            queryset = queryset.order_by("-created")

            # Pagination
            paginator = Paginator(queryset, limit)
            page_obj = paginator.get_page(page)

            # Serialize communications
            communications_data = []
            for comm in page_obj:
                comm_data = {
                    "id": comm.id,
                    "communication_type": comm.communication_type,
                    "status": comm.status,
                    "priority": comm.priority,
                    "subject": comm.subject,
                    "title": comm.title,
                    "content": comm.content,
                    "content_html": comm.content_html,
                    "category": comm.category,
                    "tags": comm.tags.split(",") if comm.tags else [],
                    "from_member": (
                        {
                            "id": comm.from_member.id,
                            "name": comm.from_member.get_full_name(),
                            "email": comm.from_member.email,
                        }
                        if comm.from_member
                        else None
                    ),
                    "from_email": comm.from_email,
                    "from_name": comm.from_name,
                    "sent_at": comm.sent_at,
                    "delivered_at": comm.delivered_at,
                    "read_at": comm.read_at,
                    "failed_at": comm.failed_at,
                    "failure_reason": comm.failure_reason,
                    "scheduled_for": comm.scheduled_for,
                    "created": comm.created,
                    "modified": comm.modified,
                }
                communications_data.append(comm_data)

            return Response(
                {
                    "communications": communications_data,
                    "pagination": {
                        "page": page,
                        "limit": limit,
                        "total_count": paginator.count,
                        "total_pages": paginator.num_pages,
                        "has_next": page_obj.has_next(),
                        "has_previous": page_obj.has_previous(),
                        "next_page": (
                            page_obj.next_page_number() if page_obj.has_next() else None
                        ),
                        "previous_page": (
                            page_obj.previous_page_number()
                            if page_obj.has_previous()
                            else None
                        ),
                    },
                    "filters": {
                        "type": communication_type,
                        "status": status,
                        "priority": priority,
                        "category": category,
                    },
                }
            )

        except Exception as e:
            return handle_api_exception(e, "API operation")

    def post(self, request):
        """Create a new communication"""
        try:
            # Check if user has permission to send communications
            if not request.user.has_permission("communication.send"):
                return Response(
                    {"error": "Access denied. Communication permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            # Get request data
            data = request.data

            # Create communication
            communication = Communication.objects.create(
                communication_type=data.get("communication_type", "email"),
                status=data.get("status", "pending"),
                priority=data.get("priority", "normal"),
                to_member_id=data.get("to_member_id"),
                to_email=data.get("to_email"),
                to_phone=data.get("to_phone"),
                from_member=request.user,
                from_email=data.get("from_email"),
                from_name=data.get("from_name"),
                subject=data.get("subject"),
                title=data.get("title"),
                content=data.get("content"),
                content_html=data.get("content_html"),
                template_name=data.get("template_name"),
                category=data.get("category"),
                tags=data.get("tags"),
                scheduled_for=data.get("scheduled_for"),
            )

            return Response(
                {
                    "success": True,
                    "message": "Communication created successfully",
                    "communication": {
                        "id": communication.id,
                        "communication_type": communication.communication_type,
                        "status": communication.status,
                        "priority": communication.priority,
                        "subject": communication.subject,
                        "title": communication.title,
                        "content": communication.content,
                        "created": communication.created,
                    },
                },
                status=drf_status.HTTP_201_CREATED,
            )

        except Exception as e:
            return handle_api_exception(e, "API operation")


class CommunicationDetailAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, communication_id):
        """Get a specific communication"""
        try:
            communication = get_object_or_404(Communication, id=communication_id)

            # Check if user has access to this communication
            if (
                communication.to_member != request.user
                and not request.user.has_permission("communication.view_all")
            ):
                return Response(
                    {
                        "error": "Access denied. You can only view your own communications."
                    },
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            return Response(
                {
                    "id": communication.id,
                    "communication_type": communication.communication_type,
                    "status": communication.status,
                    "priority": communication.priority,
                    "subject": communication.subject,
                    "title": communication.title,
                    "content": communication.content,
                    "content_html": communication.content_html,
                    "category": communication.category,
                    "tags": communication.tags.split(",") if communication.tags else [],
                    "to_member": (
                        {
                            "id": communication.to_member.id,
                            "name": communication.to_member.get_full_name(),
                            "email": communication.to_member.email,
                        }
                        if communication.to_member
                        else None
                    ),
                    "to_email": communication.to_email,
                    "to_phone": communication.to_phone,
                    "from_member": (
                        {
                            "id": communication.from_member.id,
                            "name": communication.from_member.get_full_name(),
                            "email": communication.from_member.email,
                        }
                        if communication.from_member
                        else None
                    ),
                    "from_email": communication.from_email,
                    "from_name": communication.from_name,
                    "template_name": communication.template_name,
                    "sent_at": communication.sent_at,
                    "delivered_at": communication.delivered_at,
                    "read_at": communication.read_at,
                    "failed_at": communication.failed_at,
                    "failure_reason": communication.failure_reason,
                    "retry_count": communication.retry_count,
                    "max_retries": communication.max_retries,
                    "scheduled_for": communication.scheduled_for,
                    "created": communication.created,
                    "modified": communication.modified,
                }
            )

        except Exception as e:
            return handle_api_exception(e, "API operation")

    def patch(self, request, communication_id):
        """Update a communication (e.g., mark as read)"""
        try:
            communication = get_object_or_404(Communication, id=communication_id)

            # Check if user has access to this communication
            if (
                communication.to_member != request.user
                and not request.user.has_permission("communication.manage")
            ):
                return Response(
                    {
                        "error": "Access denied. You can only update your own communications."
                    },
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            data = request.data

            # Update allowed fields
            if "status" in data:
                communication.status = data["status"]
            if "read_at" in data and data["read_at"]:
                communication.read_at = data["read_at"]

            communication.save()

            return Response(
                {
                    "success": True,
                    "message": "Communication updated successfully",
                    "communication": {
                        "id": communication.id,
                        "status": communication.status,
                        "read_at": communication.read_at,
                    },
                }
            )

        except Exception as e:
            return handle_api_exception(e, "API operation")


class CommunicationsAdminAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get all communications (admin only)"""
        try:
            # Check if user has admin access permission
            if not request.user.has_permission("communication.view_all"):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            # Get query parameters
            communication_type = request.GET.get("type", "")
            status = request.GET.get("status", "")
            priority = request.GET.get("priority", "")
            category = request.GET.get("category", "")
            to_member = request.GET.get("to_member", "")
            from_member = request.GET.get("from_member", "")
            page = int(request.GET.get("page", 1))
            limit = int(request.GET.get("limit", 20))

            # Start with all communications
            queryset = Communication.objects.select_related("to_member", "from_member")

            # Apply filters
            if communication_type:
                queryset = queryset.filter(communication_type=communication_type)
            if status:
                queryset = queryset.filter(status=status)
            if priority:
                queryset = queryset.filter(priority=priority)
            if category:
                queryset = queryset.filter(category=category)
            if to_member:
                queryset = queryset.filter(to_member__id=to_member)
            if from_member:
                queryset = queryset.filter(from_member__id=from_member)

            # Order by created date (newest first)
            queryset = queryset.order_by("-created")

            # Pagination
            paginator = Paginator(queryset, limit)
            page_obj = paginator.get_page(page)

            # Serialize communications
            communications_data = []
            for comm in page_obj:
                comm_data = {
                    "id": comm.id,
                    "communication_type": comm.communication_type,
                    "status": comm.status,
                    "priority": comm.priority,
                    "subject": comm.subject,
                    "title": comm.title,
                    "content": (
                        comm.content[:200] + "..."
                        if len(comm.content) > 200
                        else comm.content
                    ),
                    "category": comm.category,
                    "tags": comm.tags.split(",") if comm.tags else [],
                    "to_member": (
                        {
                            "id": comm.to_member.id,
                            "name": comm.to_member.get_full_name(),
                            "email": comm.to_member.email,
                        }
                        if comm.to_member
                        else None
                    ),
                    "to_email": comm.to_email,
                    "to_phone": comm.to_phone,
                    "from_member": (
                        {
                            "id": comm.from_member.id,
                            "name": comm.from_member.get_full_name(),
                            "email": comm.from_member.email,
                        }
                        if comm.from_member
                        else None
                    ),
                    "from_email": comm.from_email,
                    "from_name": comm.from_name,
                    "sent_at": comm.sent_at,
                    "delivered_at": comm.delivered_at,
                    "read_at": comm.read_at,
                    "failed_at": comm.failed_at,
                    "scheduled_for": comm.scheduled_for,
                    "created": comm.created,
                    "modified": comm.modified,
                }
                communications_data.append(comm_data)

            # Calculate statistics
            total_communications = paginator.count
            status_counts = Communication.objects.values("status").annotate(
                count=Count("id")
            )
            type_counts = Communication.objects.values("communication_type").annotate(
                count=Count("id")
            )

            return Response(
                {
                    "communications": communications_data,
                    "pagination": {
                        "page": page,
                        "limit": limit,
                        "total_count": total_communications,
                        "total_pages": paginator.num_pages,
                        "has_next": page_obj.has_next(),
                        "has_previous": page_obj.has_previous(),
                        "next_page": (
                            page_obj.next_page_number() if page_obj.has_next() else None
                        ),
                        "previous_page": (
                            page_obj.previous_page_number()
                            if page_obj.has_previous()
                            else None
                        ),
                    },
                    "statistics": {
                        "total_communications": total_communications,
                        "status_distribution": {
                            item["status"]: item["count"] for item in status_counts
                        },
                        "type_distribution": {
                            item["communication_type"]: item["count"]
                            for item in type_counts
                        },
                    },
                    "filters": {
                        "type": communication_type,
                        "status": status,
                        "priority": priority,
                        "category": category,
                        "to_member": to_member,
                        "from_member": from_member,
                    },
                }
            )

        except Exception as e:
            return handle_api_exception(e, "API operation")


class PasswordResetRequestAPI(APIView):
    """Request a password reset for a member"""

    permission_classes = []  # No authentication required for password reset requests

    def post(self, request):
        """Request a password reset"""
        try:
            data = request.data
            email = data.get("email")
            username = data.get("username")

            if not email and not username:
                return Response(
                    {"error": "Email or username is required"},
                    status=drf_status.HTTP_400_BAD_REQUEST,
                )

            # Find the member
            member = None
            if email:
                member = Member.objects.filter(email=email, is_active=True).first()
            elif username:
                member = Member.objects.filter(
                    username=username, is_active=True
                ).first()

            if not member:
                # Don't reveal if user exists or not for security
                return Response(
                    {
                        "success": True,
                        "message": "If the email/username exists, a password reset link has been sent.",
                    },
                    status=drf_status.HTTP_200_OK,
                )

            # Generate reset link (you might want to use a proper token library)
            import hashlib
            import time

            # Create a unique reset token
            token_data = f"{member.id}:{member.email}:{time.time()}"
            reset_token = hashlib.sha256(token_data.encode()).hexdigest()

            # Create or update password reset record
            reset_link = f"http://localhost:8000/reset-password/{reset_token}/"

            # Delete any existing password reset records for this member
            PasswordReset.objects.filter(member=member).delete()

            # Create new password reset record
            PasswordReset.objects.create(member=member, link=reset_link)

            # Send email or return link based on SEND_EMAILS setting
            if settings.SEND_EMAILS:
                # Send actual email
                try:
                    # Prepare email context
                    context = {
                        "member": member,
                        "reset_link": reset_link,
                        "site_name": "XD Incentives",
                    }

                    # Render email templates
                    html_message = render_to_string(
                        settings.PASSWORD_RESET_EMAIL_TEMPLATE, context
                    )
                    text_message = render_to_string(
                        settings.PASSWORD_RESET_EMAIL_TEXT_TEMPLATE, context
                    )

                    # Send email
                    send_mail(
                        subject=settings.PASSWORD_RESET_EMAIL_SUBJECT,
                        message=text_message,
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=[member.email],
                        html_message=html_message,
                        fail_silently=False,
                    )

                    return Response(
                        {
                            "success": True,
                            "message": "Password reset link has been sent to your email.",
                            "member": {
                                "id": member.id,
                                "username": member.username,
                                "email": member.email,
                            },
                        },
                        status=drf_status.HTTP_200_OK,
                    )

                except Exception as e:
                    return handle_api_exception(e, "password reset email sending")
            else:
                # Development mode: return link in response
                return Response(
                    {
                        "success": True,
                        "message": "Password reset link has been generated (development mode).",
                        "reset_link": reset_link,
                        "member": {
                            "id": member.id,
                            "username": member.username,
                            "email": member.email,
                        },
                    },
                    status=drf_status.HTTP_200_OK,
                )

        except Exception as e:
            return handle_api_exception(e, "password reset request")


class PasswordResetValidateAPI(APIView):
    """Validate a password reset token"""

    permission_classes = []  # No authentication required for token validation

    def post(self, request):
        """Validate a password reset token"""
        try:
            data = request.data
            reset_token = data.get("token")

            if not reset_token:
                return Response(
                    {"error": "Reset token is required"},
                    status=drf_status.HTTP_400_BAD_REQUEST,
                )

            # Find the password reset record
            password_reset = PasswordReset.objects.filter(
                link__contains=reset_token
            ).first()

            if not password_reset:
                return Response(
                    {"error": "Invalid or expired reset token"},
                    status=drf_status.HTTP_400_BAD_REQUEST,
                )

            # Check if the reset is recent (within 24 hours)
            from datetime import timedelta

            if password_reset.created < timezone.now() - timedelta(hours=24):
                return Response(
                    {"error": "Reset token has expired"},
                    status=drf_status.HTTP_400_BAD_REQUEST,
                )

            return Response(
                {
                    "success": True,
                    "message": "Reset token is valid",
                    "member": {
                        "id": password_reset.member.id,
                        "username": password_reset.member.username,
                        "email": password_reset.member.email,
                    },
                    "reset_id": password_reset.id,
                },
                status=drf_status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"error": f"Error validating reset token: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PasswordResetConfirmAPI(APIView):
    """Confirm password reset with new password"""

    permission_classes = (
        []
    )  # No authentication required for password reset confirmation

    def post(self, request):
        """Confirm password reset"""
        try:
            data = request.data
            reset_token = data.get("token")
            new_password = data.get("new_password")
            confirm_password = data.get("confirm_password")

            if not reset_token or not new_password or not confirm_password:
                return Response(
                    {"error": "Token, new password, and confirm password are required"},
                    status=drf_status.HTTP_400_BAD_REQUEST,
                )

            if new_password != confirm_password:
                return Response(
                    {"error": "Passwords do not match"},
                    status=drf_status.HTTP_400_BAD_REQUEST,
                )

            # Validate password strength
            if len(new_password) < 8:
                return Response(
                    {"error": "Password must be at least 8 characters long"},
                    status=drf_status.HTTP_400_BAD_REQUEST,
                )

            # Find the password reset record
            password_reset = PasswordReset.objects.filter(
                link__contains=reset_token
            ).first()

            if not password_reset:
                return Response(
                    {"error": "Invalid or expired reset token"},
                    status=drf_status.HTTP_400_BAD_REQUEST,
                )

            # Check if the reset is recent (within 24 hours)
            from datetime import timedelta

            if password_reset.created < timezone.now() - timedelta(hours=24):
                return Response(
                    {"error": "Reset token has expired"},
                    status=drf_status.HTTP_400_BAD_REQUEST,
                )

            # Update the member's password
            member = password_reset.member
            member.set_password(new_password)
            member.save()

            # Delete the password reset record
            password_reset.delete()

            return Response(
                {
                    "success": True,
                    "message": "Password has been successfully reset",
                    "member": {
                        "id": member.id,
                        "username": member.username,
                        "email": member.email,
                    },
                },
                status=drf_status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"error": f"Error resetting password: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PasswordResetHistoryAPI(APIView):
    """Get password reset history for a member (admin only)"""

    permission_classes = [IsAuthenticated]

    def get(self, request, member_id=None):
        """Get password reset history"""
        try:
            # Check if user has admin permission or is superuser
            if not (
                request.user.is_superuser
                or request.user.has_permission("member.member_password_reset.manager")
            ):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            if member_id:
                # Get history for specific member
                member = get_object_or_404(Member, id=member_id)
                password_resets = PasswordReset.objects.filter(member=member).order_by(
                    "-created"
                )
            else:
                # Get all password resets
                password_resets = PasswordReset.objects.select_related(
                    "member"
                ).order_by("-created")

            # Apply filters
            page = int(request.GET.get("page", 1))
            limit = int(request.GET.get("limit", 20))

            # Pagination
            paginator = Paginator(password_resets, limit)
            page_obj = paginator.get_page(page)

            # Serialize data
            reset_data = []
            for reset in page_obj:
                reset_info = {
                    "id": reset.id,
                    "member": {
                        "id": reset.member.id,
                        "username": reset.member.username,
                        "email": reset.member.email,
                        "name": f"{reset.member.first_name} {reset.member.last_name}".strip(),
                    },
                    "link": reset.link,
                    "created": reset.created,
                    "modified": reset.modified,
                    "is_expired": reset.created < timezone.now() - timedelta(hours=24),
                }
                reset_data.append(reset_info)

            return Response(
                {
                    "password_resets": reset_data,
                    "pagination": {
                        "page": page,
                        "limit": limit,
                        "total_count": paginator.count,
                        "total_pages": paginator.num_pages,
                        "has_next": page_obj.has_next(),
                        "has_previous": page_obj.has_previous(),
                        "next_page": (
                            page_obj.next_page_number() if page_obj.has_next() else None
                        ),
                        "previous_page": (
                            page_obj.previous_page_number()
                            if page_obj.has_previous()
                            else None
                        ),
                    },
                }
            )

        except Exception as e:
            return Response(
                {"error": f"Error retrieving password reset history: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PasswordResetStatsAPI(APIView):
    """Get password reset statistics (admin only)"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get password reset statistics"""
        # Check if user has admin permissions
        if not (
            request.user.is_superuser
            or request.user.has_permission("member.member_password_reset.manager")
        ):
            return Response(
                {"error": "Access denied. Admin permission required."},
                status=drf_status.HTTP_403_FORBIDDEN,
            )

        try:
            # Get total password resets
            total_resets = PasswordReset.objects.count()

            # Get resets by date (last 30 days)
            thirty_days_ago = timezone.now() - timedelta(days=30)
            recent_resets = PasswordReset.objects.filter(
                created__gte=thirty_days_ago
            ).count()

            # Get resets by member
            member_reset_counts = (
                PasswordReset.objects.values("member__username")
                .annotate(reset_count=Count("id"))
                .order_by("-reset_count")[:10]
            )

            # Get resets by day (last 7 days)
            seven_days_ago = timezone.now() - timedelta(days=7)
            daily_resets = (
                PasswordReset.objects.filter(created__gte=seven_days_ago)
                .extra(select={"day": "date(created)"})
                .values("day")
                .annotate(count=Count("id"))
                .order_by("day")
            )

            return Response(
                {
                    "total_resets": total_resets,
                    "recent_resets_30_days": recent_resets,
                    "top_members_by_resets": list(member_reset_counts),
                    "daily_resets_7_days": list(daily_resets),
                    "generated_at": timezone.now(),
                }
            )
        except Exception as e:
            return Response(
                {"error": f"Error getting password reset stats: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# --- Member Profile Management API --- #


class MemberProfileAPI(APIView):
    """Get and update current member's profile"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get current member's full profile"""
        try:
            member = request.user

            # Get member's teams
            teams = []
            for membership in member.team_memberships.all():
                teams.append(
                    {
                        "id": membership.team.id,
                        "name": membership.team.name,
                        "team_type": membership.team.team_type,
                        "role": membership.role,
                        "is_primary": membership.is_primary,
                        "start_date": membership.start_date,
                        "end_date": membership.end_date,
                    }
                )

            # Get member's managers
            managers = []
            for hierarchy in member.managers.all():
                managers.append(
                    {
                        "id": hierarchy.manager.id,
                        "username": hierarchy.manager.username,
                        "full_name": hierarchy.manager.get_full_name(),
                        "email": hierarchy.manager.email,
                        "relationship_type": hierarchy.relationship_type,
                        "is_primary": hierarchy.is_primary,
                        "start_date": hierarchy.start_date,
                        "end_date": hierarchy.end_date,
                    }
                )

            # Get member's subordinates
            subordinates = []
            for hierarchy in member.subordinates.all():
                subordinates.append(
                    {
                        "id": hierarchy.member.id,
                        "username": hierarchy.member.username,
                        "full_name": hierarchy.member.get_full_name(),
                        "email": hierarchy.member.email,
                        "relationship_type": hierarchy.relationship_type,
                        "is_primary": hierarchy.is_primary,
                        "start_date": hierarchy.start_date,
                        "end_date": hierarchy.end_date,
                    }
                )

            # Get terms and privacy acceptance status
            current_terms = Terms.objects.filter(current=True).first()
            current_privacy = Privacy.objects.filter(current=True).first()

            profile_data = {
                # Basic Information
                "id": member.id,
                "username": member.username,
                "email": member.email,
                "first_name": member.first_name,
                "last_name": member.last_name,
                "full_name": member.get_full_name(),
                "status": member.status,
                "tier": member.tier,
                "lang": member.lang,
                "two_factor_auth_method": member.two_factor_auth_method,
                "feature_flags": member.feature_flags,
                "notes": member.notes,
                # Member Type
                "member_type": (
                    {
                        "id": member.member_type.id,
                        "name": member.member_type.name,
                        "slug": member.member_type.slug,
                        "description": member.member_type.description,
                    }
                    if member.member_type
                    else None
                ),
                # Region
                "region": (
                    {
                        "id": member.region.id,
                        "title": member.region.title,
                        "can_signup": member.region.can_signup,
                    }
                    if member.region
                    else None
                ),
                # Work Address
                "work_address": {
                    "address1": member.work_address1,
                    "address2": member.work_address2,
                    "city": member.work_city,
                    "state": member.work_state,
                    "postal": member.work_postal,
                    "country": member.work_country,
                },
                # Home Address
                "home_address": {
                    "address1": member.home_address1,
                    "address2": member.home_address2,
                    "city": member.home_city,
                    "state": member.home_state,
                    "postal": member.home_postal,
                    "country": member.home_country,
                },
                # Contact Information
                "contact_info": {
                    "contact_at_work": member.contact_at_work,
                    "phone_work": member.phone_work,
                    "phone_home": member.phone_home,
                    "phone_cell": member.phone_cell,
                },
                # Teams
                "teams": teams,
                # Hierarchy
                "managers": managers,
                "subordinates": subordinates,
                # Terms and Privacy
                "terms_acceptance": {
                    "has_accepted_current": (
                        member.has_accepted_terms(current_terms)
                        if current_terms
                        else False
                    ),
                    "current_terms_id": current_terms.id if current_terms else None,
                    "current_terms_title": (
                        current_terms.title if current_terms else None
                    ),
                    "last_accepted_terms": (
                        member.get_latest_terms_accepted().title
                        if member.get_latest_terms_accepted()
                        else None
                    ),
                },
                "privacy_acceptance": {
                    "has_accepted_current": (
                        member.has_accepted_privacy(current_privacy)
                        if current_privacy
                        else False
                    ),
                    "current_privacy_id": (
                        current_privacy.id if current_privacy else None
                    ),
                    "current_privacy_title": (
                        current_privacy.title if current_privacy else None
                    ),
                    "last_accepted_privacy": (
                        member.get_latest_privacy_accepted().title
                        if member.get_latest_privacy_accepted()
                        else None
                    ),
                },
                # System Information
                "is_active": member.is_active,
                "is_staff": member.is_staff,
                "is_superuser": member.is_superuser,
                "date_joined": member.date_joined,
                "last_login": member.last_login,
                "created": member.created,
                "modified": member.modified,
            }

            return Response(profile_data)
        except Exception as e:
            return Response(
                {"error": f"Error retrieving profile: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def put(self, request):
        """Update current member's profile"""
        try:
            member = request.user
            data = request.data

            # Fields that members can update themselves
            allowed_fields = [
                "first_name",
                "last_name",
                "email",
                "work_address1",
                "work_address2",
                "work_city",
                "work_state",
                "work_postal",
                "work_country",
                "home_address1",
                "home_address2",
                "home_city",
                "home_state",
                "home_postal",
                "home_country",
                "contact_at_work",
                "phone_work",
                "phone_home",
                "phone_cell",
                "lang",
                "two_factor_auth_method",
                "notes",
            ]

            # Update allowed fields
            for field in allowed_fields:
                if field in data:
                    setattr(member, field, data[field])

            # Validate email uniqueness if email is being changed
            if "email" in data and data["email"] != member.email:
                if (
                    Member.objects.filter(email=data["email"])
                    .exclude(id=member.id)
                    .exists()
                ):
                    return Response(
                        {"error": "Email address is already in use"},
                        status=drf_status.HTTP_400_BAD_REQUEST,
                    )

            member.save()

            return Response(
                {
                    "success": True,
                    "message": "Profile updated successfully",
                    "member": {
                        "id": member.id,
                        "username": member.username,
                        "email": member.email,
                        "full_name": member.get_full_name(),
                    },
                }
            )
        except Exception as e:
            return Response(
                {"error": f"Error updating profile: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def patch(self, request):
        """Partial update of current member's profile"""
        try:
            member = request.user
            data = request.data

            # Fields that members can update themselves
            allowed_fields = [
                "first_name",
                "last_name",
                "email",
                "work_address1",
                "work_address2",
                "work_city",
                "work_state",
                "work_postal",
                "work_country",
                "home_address1",
                "home_address2",
                "home_city",
                "home_state",
                "home_postal",
                "home_country",
                "contact_at_work",
                "phone_work",
                "phone_home",
                "phone_cell",
                "lang",
                "two_factor_auth_method",
                "notes",
            ]

            # Update only provided fields
            for field in allowed_fields:
                if field in data:
                    setattr(member, field, data[field])

            # Validate email uniqueness if email is being changed
            if "email" in data and data["email"] != member.email:
                if (
                    Member.objects.filter(email=data["email"])
                    .exclude(id=member.id)
                    .exists()
                ):
                    return Response(
                        {"error": "Email address is already in use"},
                        status=drf_status.HTTP_400_BAD_REQUEST,
                    )

            member.save()

            return Response(
                {
                    "success": True,
                    "message": "Profile partially updated successfully",
                    "member": {
                        "id": member.id,
                        "username": member.username,
                        "email": member.email,
                        "full_name": member.get_full_name(),
                    },
                }
            )
        except Exception as e:
            return Response(
                {"error": f"Error updating profile: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class MemberProfileDetailAPI(APIView):
    """Get and update specific member's profile (admin only)"""

    permission_classes = [IsAuthenticated]

    def get(self, request, member_id):
        """Get specific member's profile (admin only)"""
        try:
            # Check if user has permission to view other members
            if not (
                request.user.is_superuser
                or request.user.has_permission("member.member.manager")
            ):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            member = get_object_or_404(Member, id=member_id)

            # Get member's teams
            teams = []
            for membership in member.team_memberships.all():
                teams.append(
                    {
                        "id": membership.team.id,
                        "name": membership.team.name,
                        "team_type": membership.team.team_type,
                        "role": membership.role,
                        "is_primary": membership.is_primary,
                        "start_date": membership.start_date,
                        "end_date": membership.end_date,
                    }
                )

            # Get member's managers
            managers = []
            for hierarchy in member.managers.all():
                managers.append(
                    {
                        "id": hierarchy.manager.id,
                        "username": hierarchy.manager.username,
                        "full_name": hierarchy.manager.get_full_name(),
                        "email": hierarchy.manager.email,
                        "relationship_type": hierarchy.relationship_type,
                        "is_primary": hierarchy.is_primary,
                        "start_date": hierarchy.start_date,
                        "end_date": hierarchy.end_date,
                    }
                )

            # Get member's subordinates
            subordinates = []
            for hierarchy in member.subordinates.all():
                subordinates.append(
                    {
                        "id": hierarchy.member.id,
                        "username": hierarchy.member.username,
                        "full_name": hierarchy.member.get_full_name(),
                        "email": hierarchy.member.email,
                        "relationship_type": hierarchy.relationship_type,
                        "is_primary": hierarchy.is_primary,
                        "start_date": hierarchy.start_date,
                        "end_date": hierarchy.end_date,
                    }
                )

            # Get terms and privacy acceptance status
            current_terms = Terms.objects.filter(current=True).first()
            current_privacy = Privacy.objects.filter(current=True).first()

            profile_data = {
                # Basic Information
                "id": member.id,
                "username": member.username,
                "email": member.email,
                "first_name": member.first_name,
                "last_name": member.last_name,
                "full_name": member.get_full_name(),
                "status": member.status,
                "tier": member.tier,
                "lang": member.lang,
                "two_factor_auth_method": member.two_factor_auth_method,
                "feature_flags": member.feature_flags,
                "notes": member.notes,
                # Member Type
                "member_type": (
                    {
                        "id": member.member_type.id,
                        "name": member.member_type.name,
                        "slug": member.member_type.slug,
                        "description": member.member_type.description,
                    }
                    if member.member_type
                    else None
                ),
                # Region
                "region": (
                    {
                        "id": member.region.id,
                        "title": member.region.title,
                        "can_signup": member.region.can_signup,
                    }
                    if member.region
                    else None
                ),
                # Work Address
                "work_address": {
                    "address1": member.work_address1,
                    "address2": member.work_address2,
                    "city": member.work_city,
                    "state": member.work_state,
                    "postal": member.work_postal,
                    "country": member.work_country,
                },
                # Home Address
                "home_address": {
                    "address1": member.home_address1,
                    "address2": member.home_address2,
                    "city": member.home_city,
                    "state": member.home_state,
                    "postal": member.home_postal,
                    "country": member.home_country,
                },
                # Contact Information
                "contact_info": {
                    "contact_at_work": member.contact_at_work,
                    "phone_work": member.phone_work,
                    "phone_home": member.phone_home,
                    "phone_cell": member.phone_cell,
                },
                # Teams
                "teams": teams,
                # Hierarchy
                "managers": managers,
                "subordinates": subordinates,
                # Terms and Privacy
                "terms_acceptance": {
                    "has_accepted_current": (
                        member.has_accepted_terms(current_terms)
                        if current_terms
                        else False
                    ),
                    "current_terms_id": current_terms.id if current_terms else None,
                    "current_terms_title": (
                        current_terms.title if current_terms else None
                    ),
                    "last_accepted_terms": (
                        member.get_latest_terms_accepted().title
                        if member.get_latest_terms_accepted()
                        else None
                    ),
                },
                "privacy_acceptance": {
                    "has_accepted_current": (
                        member.has_accepted_privacy(current_privacy)
                        if current_privacy
                        else False
                    ),
                    "current_privacy_id": (
                        current_privacy.id if current_privacy else None
                    ),
                    "current_privacy_title": (
                        current_privacy.title if current_privacy else None
                    ),
                    "last_accepted_privacy": (
                        member.get_latest_privacy_accepted().title
                        if member.get_latest_privacy_accepted()
                        else None
                    ),
                },
                # Approval Information
                "approval": {
                    "approved_by": (
                        {
                            "id": member.approved_by.id,
                            "username": member.approved_by.username,
                            "full_name": member.approved_by.get_full_name(),
                        }
                        if member.approved_by
                        else None
                    ),
                    "approved_date": member.approved_date,
                    "denied_by": (
                        {
                            "id": member.denied_by.id,
                            "username": member.denied_by.username,
                            "full_name": member.denied_by.get_full_name(),
                        }
                        if member.denied_by
                        else None
                    ),
                    "denied_date": member.denied_date,
                },
                # System Information
                "is_active": member.is_active,
                "is_staff": member.is_staff,
                "is_superuser": member.is_superuser,
                "date_joined": member.date_joined,
                "last_login": member.last_login,
                "created": member.created,
                "modified": member.modified,
            }

            return Response(profile_data)
        except Exception as e:
            return Response(
                {"error": f"Error retrieving member profile: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def put(self, request, member_id):
        """Update specific member's profile (admin only)"""
        try:
            # Check if user has permission to update other members
            if not (
                request.user.is_superuser
                or request.user.has_permission("member.member.manager")
            ):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            member = get_object_or_404(Member, id=member_id)
            data = request.data

            # All fields that admins can update
            allowed_fields = [
                "username",
                "email",
                "first_name",
                "last_name",
                "status",
                "tier",
                "member_type",
                "region",
                "work_address1",
                "work_address2",
                "work_city",
                "work_state",
                "work_postal",
                "work_country",
                "home_address1",
                "home_address2",
                "home_city",
                "home_state",
                "home_postal",
                "home_country",
                "contact_at_work",
                "phone_work",
                "phone_home",
                "phone_cell",
                "lang",
                "two_factor_auth_method",
                "feature_flags",
                "notes",
                "is_active",
                "is_staff",
                "is_superuser",
            ]

            # Update allowed fields
            for field in allowed_fields:
                if field in data:
                    if field == "member_type" and data[field]:
                        member_type = get_object_or_404(MemberType, id=data[field])
                        member.member_type = member_type
                    elif field == "region" and data[field]:
                        region = get_object_or_404(Region, id=data[field])
                        member.region = region
                    else:
                        setattr(member, field, data[field])

            # Validate email uniqueness if email is being changed
            if "email" in data and data["email"] != member.email:
                if (
                    Member.objects.filter(email=data["email"])
                    .exclude(id=member.id)
                    .exists()
                ):
                    return Response(
                        {"error": "Email address is already in use"},
                        status=drf_status.HTTP_400_BAD_REQUEST,
                    )

            # Validate username uniqueness if username is being changed
            if "username" in data and data["username"] != member.username:
                if (
                    Member.objects.filter(username=data["username"])
                    .exclude(id=member.id)
                    .exists()
                ):
                    return Response(
                        {"error": "Username is already in use"},
                        status=drf_status.HTTP_400_BAD_REQUEST,
                    )

            member.save()

            return Response(
                {
                    "success": True,
                    "message": "Member profile updated successfully",
                    "member": {
                        "id": member.id,
                        "username": member.username,
                        "email": member.email,
                        "full_name": member.get_full_name(),
                    },
                }
            )
        except Exception as e:
            return Response(
                {"error": f"Error updating member profile: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def patch(self, request, member_id):
        """Partial update of specific member's profile (admin only)"""
        try:
            # Check if user has permission to update other members
            if not (
                request.user.is_superuser
                or request.user.has_permission("member.member.manager")
            ):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            member = get_object_or_404(Member, id=member_id)
            data = request.data

            # All fields that admins can update
            allowed_fields = [
                "username",
                "email",
                "first_name",
                "last_name",
                "status",
                "tier",
                "member_type",
                "region",
                "work_address1",
                "work_address2",
                "work_city",
                "work_state",
                "work_postal",
                "work_country",
                "home_address1",
                "home_address2",
                "home_city",
                "home_state",
                "home_postal",
                "home_country",
                "contact_at_work",
                "phone_work",
                "phone_home",
                "phone_cell",
                "lang",
                "two_factor_auth_method",
                "feature_flags",
                "notes",
                "is_active",
                "is_staff",
                "is_superuser",
            ]

            # Update only provided fields
            for field in allowed_fields:
                if field in data:
                    if field == "member_type" and data[field]:
                        member_type = get_object_or_404(MemberType, id=data[field])
                        member.member_type = member_type
                    elif field == "region" and data[field]:
                        region = get_object_or_404(Region, id=data[field])
                        member.region = region
                    else:
                        setattr(member, field, data[field])

            # Validate email uniqueness if email is being changed
            if "email" in data and data["email"] != member.email:
                if (
                    Member.objects.filter(email=data["email"])
                    .exclude(id=member.id)
                    .exists()
                ):
                    return Response(
                        {"error": "Email address is already in use"},
                        status=drf_status.HTTP_400_BAD_REQUEST,
                    )

            # Validate username uniqueness if username is being changed
            if "username" in data and data["username"] != member.username:
                if (
                    Member.objects.filter(username=data["username"])
                    .exclude(id=member.id)
                    .exists()
                ):
                    return Response(
                        {"error": "Username is already in use"},
                        status=drf_status.HTTP_400_BAD_REQUEST,
                    )

            member.save()

            return Response(
                {
                    "success": True,
                    "message": "Member profile partially updated successfully",
                    "member": {
                        "id": member.id,
                        "username": member.username,
                        "email": member.email,
                        "full_name": member.get_full_name(),
                    },
                }
            )
        except Exception as e:
            return Response(
                {"error": f"Error updating member profile: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class MemberProfileSearchAPI(APIView):
    """Search members by various criteria (admin only)"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Search members with filters"""
        try:
            # Check if user has permission to search members
            if not (
                request.user.is_superuser
                or request.user.has_permission("member.member.manager")
            ):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            # Get query parameters
            search = request.GET.get("search", "")
            member_type = request.GET.get("member_type", "")
            region = request.GET.get("region", "")
            status = request.GET.get("status", "")
            team = request.GET.get("team", "")
            is_active = request.GET.get("is_active", "")
            page = request.GET.get("page", 1)
            page_size = min(
                int(request.GET.get("page_size", 20)), 100
            )  # Max 100 per page

            # Build queryset
            queryset = Member.objects.all()

            # Apply filters
            if search:
                queryset = queryset.filter(
                    Q(username__icontains=search)
                    | Q(email__icontains=search)
                    | Q(first_name__icontains=search)
                    | Q(last_name__icontains=search)
                    | Q(phone_cell__icontains=search)
                    | Q(phone_work__icontains=search)
                )

            if member_type:
                queryset = queryset.filter(member_type__id=member_type)

            if region:
                queryset = queryset.filter(region__id=region)

            if status:
                queryset = queryset.filter(status=status)

            if team:
                queryset = queryset.filter(team_memberships__team__id=team)

            # Note: is_active is a computed property, so we'll filter by date ranges instead
            if is_active == "true":
                # Active relationships: no end_date or end_date in the future
                queryset = queryset.filter(
                    models.Q(end_date__isnull=True)
                    | models.Q(end_date__gt=timezone.now().date())
                )
            elif is_active == "false":
                # Inactive relationships: end_date in the past
                queryset = queryset.filter(end_date__lt=timezone.now().date())

            # Remove duplicates and order
            queryset = queryset.distinct().order_by("username")

            # Pagination
            paginator = Paginator(queryset, page_size)
            try:
                members_page = paginator.page(page)
            except Exception:
                members_page = paginator.page(1)

            # Serialize results
            members_data = []
            for member in members_page:
                members_data.append(
                    {
                        "id": member.id,
                        "username": member.username,
                        "email": member.email,
                        "first_name": member.first_name,
                        "last_name": member.last_name,
                        "full_name": member.get_full_name(),
                        "status": member.status,
                        "tier": member.tier,
                        "member_type": (
                            {
                                "id": member.member_type.id,
                                "name": member.member_type.name,
                                "slug": member.member_type.slug,
                            }
                            if member.member_type
                            else None
                        ),
                        "region": (
                            {
                                "id": member.region.id,
                                "title": member.region.title,
                            }
                            if member.region
                            else None
                        ),
                        "phone_cell": member.phone_cell,
                        "phone_work": member.phone_work,
                        "is_active": member.is_active,
                        "is_staff": member.is_staff,
                        "is_superuser": member.is_superuser,
                        "date_joined": member.date_joined,
                        "last_login": member.last_login,
                        "created": member.created,
                    }
                )

            return Response(
                {
                    "members": members_data,
                    "pagination": {
                        "page": members_page.number,
                        "pages": members_page.paginator.num_pages,
                        "total": members_page.paginator.count,
                        "has_next": members_page.has_next(),
                        "has_previous": members_page.has_previous(),
                    },
                    "filters": {
                        "search": search,
                        "member_type": member_type,
                        "region": region,
                        "status": status,
                        "team": team,
                        "is_active": is_active,
                    },
                }
            )
        except Exception as e:
            return Response(
                {"error": f"Error searching members: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# --- Member Hierarchy Management API --- #


class MemberHierarchyManagementAPI(APIView):
    """Get and manage member hierarchy relationships"""

    permission_classes = [IsAuthenticated]

    def get(self, request, member_id):
        """Get all hierarchy relationships for a specific member"""
        try:
            # Check if user has permission to view hierarchy
            if not (
                request.user.is_superuser
                or request.user.has_permission("member.member.manager")
            ):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            member = get_object_or_404(Member, id=member_id)

            # Get all managers for this member
            managers = []
            for hierarchy in member.managers.all():
                managers.append(
                    {
                        "id": hierarchy.id,
                        "manager": {
                            "id": hierarchy.manager.id,
                            "username": hierarchy.manager.username,
                            "full_name": hierarchy.manager.get_full_name(),
                            "email": hierarchy.manager.email,
                        },
                        "relationship_type": hierarchy.relationship_type,
                        "is_primary": hierarchy.is_primary,
                        "start_date": hierarchy.start_date,
                        "end_date": hierarchy.end_date,
                        "notes": hierarchy.notes,
                        "is_active": hierarchy.is_active,
                        "created": hierarchy.created,
                        "modified": hierarchy.modified,
                    }
                )

            # Get all subordinates for this member
            subordinates = []
            for hierarchy in member.subordinates.all():
                subordinates.append(
                    {
                        "id": hierarchy.id,
                        "subordinate": {
                            "id": hierarchy.member.id,
                            "username": hierarchy.member.username,
                            "full_name": hierarchy.member.get_full_name(),
                            "email": hierarchy.member.email,
                        },
                        "relationship_type": hierarchy.relationship_type,
                        "is_primary": hierarchy.is_primary,
                        "start_date": hierarchy.start_date,
                        "end_date": hierarchy.end_date,
                        "notes": hierarchy.notes,
                        "is_active": hierarchy.is_active,
                        "created": hierarchy.created,
                        "modified": hierarchy.modified,
                    }
                )

            return Response(
                {
                    "member": {
                        "id": member.id,
                        "username": member.username,
                        "full_name": member.get_full_name(),
                        "email": member.email,
                    },
                    "managers": managers,
                    "subordinates": subordinates,
                    "statistics": {
                        "total_managers": len(managers),
                        "active_managers": len([m for m in managers if m["is_active"]]),
                        "primary_managers": len(
                            [m for m in managers if m["is_primary"]]
                        ),
                        "total_subordinates": len(subordinates),
                        "active_subordinates": len(
                            [s for s in subordinates if s["is_active"]]
                        ),
                        "primary_subordinates": len(
                            [s for s in subordinates if s["is_primary"]]
                        ),
                    },
                }
            )
        except Exception as e:
            return Response(
                {"error": f"Error retrieving hierarchy: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def post(self, request, member_id):
        """Add a new hierarchy relationship"""
        try:
            # Check if user has permission to manage hierarchy
            if not (
                request.user.is_superuser
                or request.user.has_permission("member.member.manager")
            ):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            member = get_object_or_404(Member, id=member_id)
            data = request.data

            # Validate required fields
            required_fields = ["manager_id", "relationship_type"]
            for field in required_fields:
                if field not in data:
                    return Response(
                        {"error": f"Missing required field: {field}"},
                        status=drf_status.HTTP_400_BAD_REQUEST,
                    )

            manager = get_object_or_404(Member, id=data["manager_id"])

            # Check if relationship already exists
            existing_relationship = MemberHierarchy.objects.filter(
                member=member,
                manager=manager,
                relationship_type=data["relationship_type"],
            ).first()

            if existing_relationship:
                return Response(
                    {"error": "This hierarchy relationship already exists"},
                    status=drf_status.HTTP_400_BAD_REQUEST,
                )

            # Create new relationship
            hierarchy = MemberHierarchy.objects.create(
                member=member,
                manager=manager,
                relationship_type=data["relationship_type"],
                is_primary=data.get("is_primary", False),
                start_date=data.get("start_date"),
                end_date=data.get("end_date"),
                notes=data.get("notes", ""),
            )

            return Response(
                {
                    "success": True,
                    "message": "Hierarchy relationship created successfully",
                    "hierarchy": {
                        "id": hierarchy.id,
                        "member": {
                            "id": member.id,
                            "username": member.username,
                            "full_name": member.get_full_name(),
                        },
                        "manager": {
                            "id": manager.id,
                            "username": manager.username,
                            "full_name": manager.get_full_name(),
                        },
                        "relationship_type": hierarchy.relationship_type,
                        "is_primary": hierarchy.is_primary,
                        "start_date": hierarchy.start_date,
                        "end_date": hierarchy.end_date,
                        "notes": hierarchy.notes,
                        "is_active": hierarchy.is_active,
                    },
                },
                status=drf_status.HTTP_201_CREATED,
            )
        except Exception as e:
            return Response(
                {"error": f"Error creating hierarchy relationship: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class MemberHierarchyDetailAPI(APIView):
    """Manage specific hierarchy relationships"""

    permission_classes = [IsAuthenticated]

    def get(self, request, hierarchy_id):
        """Get specific hierarchy relationship details"""
        try:
            # Check if user has permission to view hierarchy
            if not (
                request.user.is_superuser
                or request.user.has_permission("member.member.manager")
            ):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            hierarchy = get_object_or_404(MemberHierarchy, id=hierarchy_id)

            return Response(
                {
                    "id": hierarchy.id,
                    "member": {
                        "id": hierarchy.member.id,
                        "username": hierarchy.member.username,
                        "full_name": hierarchy.member.get_full_name(),
                        "email": hierarchy.member.email,
                    },
                    "manager": {
                        "id": hierarchy.manager.id,
                        "username": hierarchy.manager.username,
                        "full_name": hierarchy.manager.get_full_name(),
                        "email": hierarchy.manager.email,
                    },
                    "relationship_type": hierarchy.relationship_type,
                    "is_primary": hierarchy.is_primary,
                    "start_date": hierarchy.start_date,
                    "end_date": hierarchy.end_date,
                    "notes": hierarchy.notes,
                    "is_active": hierarchy.is_active,
                    "created": hierarchy.created,
                    "modified": hierarchy.modified,
                }
            )
        except Exception as e:
            return Response(
                {"error": f"Error retrieving hierarchy relationship: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def put(self, request, hierarchy_id):
        """Update specific hierarchy relationship"""
        try:
            # Check if user has permission to manage hierarchy
            if not (
                request.user.is_superuser
                or request.user.has_permission("member.member.manager")
            ):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            hierarchy = get_object_or_404(MemberHierarchy, id=hierarchy_id)
            data = request.data

            # Update fields
            if "relationship_type" in data:
                hierarchy.relationship_type = data["relationship_type"]
            if "is_primary" in data:
                hierarchy.is_primary = data["is_primary"]
            if "start_date" in data:
                hierarchy.start_date = data["start_date"]
            if "end_date" in data:
                hierarchy.end_date = data["end_date"]
            if "notes" in data:
                hierarchy.notes = data["notes"]

            hierarchy.save()

            return Response(
                {
                    "success": True,
                    "message": "Hierarchy relationship updated successfully",
                    "hierarchy": {
                        "id": hierarchy.id,
                        "member": {
                            "id": hierarchy.member.id,
                            "username": hierarchy.member.username,
                            "full_name": hierarchy.member.get_full_name(),
                        },
                        "manager": {
                            "id": hierarchy.manager.id,
                            "username": hierarchy.manager.username,
                            "full_name": hierarchy.manager.get_full_name(),
                        },
                        "relationship_type": hierarchy.relationship_type,
                        "is_primary": hierarchy.is_primary,
                        "start_date": hierarchy.start_date,
                        "end_date": hierarchy.end_date,
                        "notes": hierarchy.notes,
                        "is_active": hierarchy.is_active,
                    },
                }
            )
        except Exception as e:
            return Response(
                {"error": f"Error updating hierarchy relationship: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def patch(self, request, hierarchy_id):
        """Partial update of hierarchy relationship"""
        try:
            # Check if user has permission to manage hierarchy
            if not (
                request.user.is_superuser
                or request.user.has_permission("member.member.manager")
            ):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            hierarchy = get_object_or_404(MemberHierarchy, id=hierarchy_id)
            data = request.data

            # Update only provided fields
            if "relationship_type" in data:
                hierarchy.relationship_type = data["relationship_type"]
            if "is_primary" in data:
                hierarchy.is_primary = data["is_primary"]
            if "start_date" in data:
                hierarchy.start_date = data["start_date"]
            if "end_date" in data:
                hierarchy.end_date = data["end_date"]
            if "notes" in data:
                hierarchy.notes = data["notes"]

            hierarchy.save()

            return Response(
                {
                    "success": True,
                    "message": "Hierarchy relationship partially updated successfully",
                    "hierarchy": {
                        "id": hierarchy.id,
                        "member": {
                            "id": hierarchy.member.id,
                            "username": hierarchy.member.username,
                            "full_name": hierarchy.member.get_full_name(),
                        },
                        "manager": {
                            "id": hierarchy.manager.id,
                            "username": hierarchy.manager.username,
                            "full_name": hierarchy.manager.get_full_name(),
                        },
                        "relationship_type": hierarchy.relationship_type,
                        "is_primary": hierarchy.is_primary,
                        "start_date": hierarchy.start_date,
                        "end_date": hierarchy.end_date,
                        "notes": hierarchy.notes,
                        "is_active": hierarchy.is_active,
                    },
                }
            )
        except Exception as e:
            return Response(
                {"error": f"Error updating hierarchy relationship: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def delete(self, request, hierarchy_id):
        """Delete hierarchy relationship"""
        try:
            # Check if user has permission to manage hierarchy
            if not (
                request.user.is_superuser
                or request.user.has_permission("member.member.manager")
            ):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            hierarchy = get_object_or_404(MemberHierarchy, id=hierarchy_id)

            # Store info before deletion
            member_info = {
                "id": hierarchy.member.id,
                "username": hierarchy.member.username,
                "full_name": hierarchy.member.get_full_name(),
            }
            manager_info = {
                "id": hierarchy.manager.id,
                "username": hierarchy.manager.username,
                "full_name": hierarchy.manager.get_full_name(),
            }

            hierarchy.delete()

            return Response(
                {
                    "success": True,
                    "message": "Hierarchy relationship deleted successfully",
                    "deleted_relationship": {
                        "member": member_info,
                        "manager": manager_info,
                        "relationship_type": hierarchy.relationship_type,
                        "is_primary": hierarchy.is_primary,
                    },
                }
            )
        except Exception as e:
            return Response(
                {"error": f"Error deleting hierarchy relationship: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class MemberHierarchyBulkAPI(APIView):
    """Bulk operations for hierarchy management"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Bulk create hierarchy relationships"""
        try:
            # Check if user has permission to manage hierarchy
            if not (
                request.user.is_superuser
                or request.user.has_permission("member.member.manager")
            ):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            data = request.data
            relationships = data.get("relationships", [])

            if not relationships:
                return Response(
                    {"error": "No relationships provided"},
                    status=drf_status.HTTP_400_BAD_REQUEST,
                )

            created_relationships = []
            errors = []

            for i, rel_data in enumerate(relationships):
                try:
                    # Validate required fields
                    required_fields = ["member_id", "manager_id", "relationship_type"]
                    for field in required_fields:
                        if field not in rel_data:
                            errors.append(
                                f"Relationship {i + 1}: Missing required field: {field}"
                            )
                            continue

                    member = get_object_or_404(Member, id=rel_data["member_id"])
                    manager = get_object_or_404(Member, id=rel_data["manager_id"])

                    # Check if relationship already exists
                    existing = MemberHierarchy.objects.filter(
                        member=member,
                        manager=manager,
                        relationship_type=rel_data["relationship_type"],
                    ).first()

                    if existing:
                        errors.append(
                            f"Relationship {i + 1}: Relationship already exists"
                        )
                        continue

                    # Create relationship
                    hierarchy = MemberHierarchy.objects.create(
                        member=member,
                        manager=manager,
                        relationship_type=rel_data["relationship_type"],
                        is_primary=rel_data.get("is_primary", False),
                        start_date=rel_data.get("start_date"),
                        end_date=rel_data.get("end_date"),
                        notes=rel_data.get("notes", ""),
                    )

                    created_relationships.append(
                        {
                            "id": hierarchy.id,
                            "member": {
                                "id": member.id,
                                "username": member.username,
                                "full_name": member.get_full_name(),
                            },
                            "manager": {
                                "id": manager.id,
                                "username": manager.username,
                                "full_name": manager.get_full_name(),
                            },
                            "relationship_type": hierarchy.relationship_type,
                            "is_primary": hierarchy.is_primary,
                        }
                    )

                except Exception as e:
                    errors.append(f"Relationship {i + 1}: {str(e)}")

            return Response(
                {
                    "success": True,
                    "message": f"Bulk hierarchy creation completed. {len(created_relationships)} created, {len(errors)} errors.",
                    "created_relationships": created_relationships,
                    "errors": errors,
                    "summary": {
                        "total_requested": len(relationships),
                        "created": len(created_relationships),
                        "errors": len(errors),
                    },
                }
            )
        except Exception as e:
            return Response(
                {"error": f"Error in bulk hierarchy creation: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def delete(self, request):
        """Bulk delete hierarchy relationships"""
        try:
            # Check if user has permission to manage hierarchy
            if not (
                request.user.is_superuser
                or request.user.has_permission("member.member.manager")
            ):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            data = request.data
            hierarchy_ids = data.get("hierarchy_ids", [])

            if not hierarchy_ids:
                return Response(
                    {"error": "No hierarchy IDs provided"},
                    status=drf_status.HTTP_400_BAD_REQUEST,
                )

            deleted_count = 0
            errors = []

            for hierarchy_id in hierarchy_ids:
                try:
                    hierarchy = get_object_or_404(MemberHierarchy, id=hierarchy_id)
                    hierarchy.delete()
                    deleted_count += 1
                except Exception as e:
                    errors.append(f"Hierarchy {hierarchy_id}: {str(e)}")

            return Response(
                {
                    "success": True,
                    "message": f"Bulk hierarchy deletion completed. {deleted_count} deleted, {len(errors)} errors.",
                    "deleted_count": deleted_count,
                    "errors": errors,
                    "summary": {
                        "total_requested": len(hierarchy_ids),
                        "deleted": deleted_count,
                        "errors": len(errors),
                    },
                }
            )
        except Exception as e:
            return Response(
                {"error": f"Error in bulk hierarchy deletion: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class MemberHierarchySearchAPI(APIView):
    """Search hierarchy relationships"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Search hierarchy relationships with filters"""
        try:
            # Check if user has permission to view hierarchy
            if not (
                request.user.is_superuser
                or request.user.has_permission("member.member.manager")
            ):
                return Response(
                    {"error": "Access denied. Admin permission required."},
                    status=drf_status.HTTP_403_FORBIDDEN,
                )

            # Get query parameters
            member_id = request.GET.get("member_id", "")
            manager_id = request.GET.get("manager_id", "")
            relationship_type = request.GET.get("relationship_type", "")
            is_primary = request.GET.get("is_primary", "")
            is_active = request.GET.get("is_active", "")
            page = request.GET.get("page", 1)
            page_size = min(
                int(request.GET.get("page_size", 20)), 100
            )  # Max 100 per page

            # Build queryset
            queryset = MemberHierarchy.objects.all()

            # Apply filters
            if member_id:
                queryset = queryset.filter(member__id=member_id)

            if manager_id:
                queryset = queryset.filter(manager__id=manager_id)

            if relationship_type:
                queryset = queryset.filter(relationship_type=relationship_type)

            if is_primary != "":
                queryset = queryset.filter(is_primary=is_primary.lower() == "true")

            # Note: is_active is a computed property, so we'll filter by date ranges instead
            if is_active == "true":
                # Active relationships: no end_date or end_date in the future
                queryset = queryset.filter(
                    models.Q(end_date__isnull=True)
                    | models.Q(end_date__gt=timezone.now().date())
                )
            elif is_active == "false":
                # Inactive relationships: end_date in the past
                queryset = queryset.filter(end_date__lt=timezone.now().date())

            # Order by creation date
            queryset = queryset.order_by("-created")

            # Pagination
            paginator = Paginator(queryset, page_size)
            try:
                hierarchies_page = paginator.page(page)
            except Exception:
                hierarchies_page = paginator.page(1)

            # Serialize results
            hierarchies_data = []
            for hierarchy in hierarchies_page:
                hierarchies_data.append(
                    {
                        "id": hierarchy.id,
                        "member": {
                            "id": hierarchy.member.id,
                            "username": hierarchy.member.username,
                            "full_name": hierarchy.member.get_full_name(),
                            "email": hierarchy.member.email,
                        },
                        "manager": {
                            "id": hierarchy.manager.id,
                            "username": hierarchy.manager.username,
                            "full_name": hierarchy.manager.get_full_name(),
                            "email": hierarchy.manager.email,
                        },
                        "relationship_type": hierarchy.relationship_type,
                        "is_primary": hierarchy.is_primary,
                        "start_date": hierarchy.start_date,
                        "end_date": hierarchy.end_date,
                        "notes": hierarchy.notes,
                        "is_active": hierarchy.is_active,
                        "created": hierarchy.created,
                        "modified": hierarchy.modified,
                    }
                )

            return Response(
                {
                    "hierarchies": hierarchies_data,
                    "pagination": {
                        "page": hierarchies_page.number,
                        "pages": hierarchies_page.paginator.num_pages,
                        "total": hierarchies_page.paginator.count,
                        "has_next": hierarchies_page.has_next(),
                        "has_previous": hierarchies_page.has_previous(),
                    },
                    "filters": {
                        "member_id": member_id,
                        "manager_id": manager_id,
                        "relationship_type": relationship_type,
                        "is_primary": is_primary,
                        "is_active": is_active,
                    },
                }
            )
        except Exception as e:
            return Response(
                {"error": f"Error searching hierarchy relationships: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# --- Clerk Authentication API --- #


class ClerkAuthAPI(APIView):
    """Clerk authentication endpoints"""

    permission_classes = []

    def post(self, request):
        """Verify Clerk token and authenticate user"""
        try:
            # Get token from request
            token = request.data.get("token")
            if not token:
                return Response(
                    {"error": "Token is required"},
                    status=drf_status.HTTP_400_BAD_REQUEST,
                )

            # Use Clerk authentication backend
            from apps.member.clerk_auth import ClerkAuthenticationBackend

            backend = ClerkAuthenticationBackend()
            user = backend.authenticate(request, token=token)

            if not user:
                return Response(
                    {"error": "Invalid token"}, status=drf_status.HTTP_401_UNAUTHORIZED
                )

            # Generate JWT token for the authenticated user
            from rest_framework_simplejwt.tokens import RefreshToken

            refresh = RefreshToken.for_user(user)

            return Response(
                {
                    "success": True,
                    "message": "Authentication successful",
                    "user": {
                        "id": user.id,
                        "username": user.username,
                        "email": user.email,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "full_name": user.get_full_name(),
                        "clerk_id": user.clerk_id,
                        "profile_image": user.profile_image,
                        "is_active": user.is_active,
                        "is_staff": user.is_staff,
                        "is_superuser": user.is_superuser,
                    },
                    "tokens": {
                        "access": str(refresh.access_token),
                        "refresh": str(refresh),
                    },
                }
            )

        except Exception as e:
            return Response(
                {"error": f"Authentication error: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ClerkUserSyncAPI(APIView):
    """Sync user data from Clerk"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Sync current user's data from Clerk"""
        try:
            # Check if user has Clerk ID
            if not request.user.clerk_id:
                return Response(
                    {"error": "User is not linked to Clerk"},
                    status=drf_status.HTTP_400_BAD_REQUEST,
                )

            # Get user data from Clerk API
            from django.conf import settings

            import requests

            headers = {
                "Authorization": f"Bearer {settings.CLERK_SECRET_KEY}",
                "Content-Type": "application/json",
            }

            response = requests.get(
                f"{settings.CLERK_API_URL}/users/{request.user.clerk_id}",
                headers=headers,
                timeout=settings.EXTERNAL_API_TIMEOUT,  # Configurable timeout to prevent hanging requests
            )

            if response.status_code != 200:
                return Response(
                    {"error": "Failed to fetch user data from Clerk"},
                    status=drf_status.HTTP_400_BAD_REQUEST,
                )

            clerk_user_data = response.json()

            # Update user with Clerk data
            from apps.member.clerk_auth import ClerkAuthenticationBackend

            backend = ClerkAuthenticationBackend()
            updated_user = backend.update_user_from_clerk(request.user, clerk_user_data)

            return Response(
                {
                    "success": True,
                    "message": "User data synced successfully",
                    "user": {
                        "id": updated_user.id,
                        "username": updated_user.username,
                        "email": updated_user.email,
                        "first_name": updated_user.first_name,
                        "last_name": updated_user.last_name,
                        "full_name": updated_user.get_full_name(),
                        "clerk_id": updated_user.clerk_id,
                        "profile_image": updated_user.profile_image,
                        "modified": updated_user.modified,
                    },
                }
            )

        except Exception as e:
            return Response(
                {"error": f"Sync error: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ClerkWebhookAPI(APIView):
    """Handle Clerk webhooks"""

    permission_classes = []

    def post(self, request):
        """Handle Clerk webhook events"""
        try:
            # Verify webhook signature
            import hashlib
            import hmac

            from django.conf import settings

            signature = request.META.get("HTTP_SVIX_SIGNATURE", "")
            timestamp = request.META.get("HTTP_SVIX_TIMESTAMP", "")

            if not signature or not timestamp:
                return Response(
                    {"error": "Missing webhook signature"},
                    status=drf_status.HTTP_400_BAD_REQUEST,
                )

            # Verify signature
            payload = request.body
            expected_signature = hmac.new(
                settings.CLERK_WEBHOOK_SECRET.encode("utf-8"),
                f"{timestamp}.{payload.decode('utf-8')}".encode("utf-8"),
                hashlib.sha256,
            ).hexdigest()

            if not hmac.compare_digest(f"v1={expected_signature}", signature):
                return Response(
                    {"error": "Invalid webhook signature"},
                    status=drf_status.HTTP_401_UNAUTHORIZED,
                )

            # Process webhook event
            event_data = request.data
            event_type = event_data.get("type")

            if event_type == "user.created":
                self.handle_user_created(event_data)
            elif event_type == "user.updated":
                self.handle_user_updated(event_data)
            elif event_type == "user.deleted":
                self.handle_user_deleted(event_data)

            return Response(
                {"success": True, "message": "Webhook processed successfully"}
            )

        except Exception as e:
            return Response(
                {"error": f"Webhook error: {str(e)}"},
                status=drf_status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def handle_user_created(self, event_data):
        """Handle user.created webhook event"""
        user_data = event_data.get("data", {})
        clerk_id = user_data.get("id")

        if clerk_id:
            from apps.member.clerk_auth import ClerkAuthenticationBackend

            backend = ClerkAuthenticationBackend()
            backend.create_user_from_clerk(user_data)

    def handle_user_updated(self, event_data):
        """Handle user.updated webhook event"""
        user_data = event_data.get("data", {})
        clerk_id = user_data.get("id")

        if clerk_id:
            try:
                user = Member.objects.get(clerk_id=clerk_id)
                from apps.member.clerk_auth import ClerkAuthenticationBackend

                backend = ClerkAuthenticationBackend()
                backend.update_user_from_clerk(user, user_data)
            except Member.DoesNotExist:
                pass

    def handle_user_deleted(self, event_data):
        """Handle user.deleted webhook event"""
        user_data = event_data.get("data", {})
        clerk_id = user_data.get("id")

        if clerk_id:
            try:
                user = Member.objects.get(clerk_id=clerk_id)
                user.is_active = False
                user.save()
            except Member.DoesNotExist:
                pass
