"""
Django settings for config project.

Generated by 'django-admin startproject' using Django 5.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

import os
from pathlib import Path

import environ

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# --- Environment Variables --- #
env = environ.Env(DEBUG=(bool, False))

# reading .env file
environ.Env.read_env(os.path.join(BASE_DIR.parent, ".env"))

DEBUG = env("DEBUG")
SECRET_KEY = env("SECRET_KEY")

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": env("DB_NAME"),
        "USER": env("DB_USER"),
        "PASSWORD": env("DB_PASSWORD"),
        "HOST": env("DB_HOST"),
        "PORT": env("DB_PORT", default="3306"),
    }
}

# Database routers for multi-tenancy
# DATABASE_ROUTERS = ['tenant.database.TenantDatabaseRouter']

# API Configuration
EXTERNAL_API_TIMEOUT = env.int("EXTERNAL_API_TIMEOUT", default=30)  # seconds

# --- END - Environment Variables --- #

# Production hosts should be configured via environment variables
ALLOWED_HOSTS = env.list("ALLOWED_HOSTS", default=["localhost", "127.0.0.1"])

# --- END - 2FA Settings --- #
LOGIN_URL = "two_factor:login"
LOGIN_REDIRECT_URL = "/"
TWO_FACTOR_PATCH_ADMIN = True  # Add 2FA to Django admin
TWO_FACTOR_SMS_GATEWAY = env("TWO_FACTOR_SMS_GATEWAY")
TWILIO_ACCOUNT_SID = env("TWILIO_ACCOUNT_SID")
TWILIO_AUTH_TOKEN = env("TWILIO_AUTH_TOKEN")
TWILIO_FROM_NUMBER = env("TWILIO_FROM_NUMBER")

PHONENUMBER_DEFAULT_REGION = "US"
PHONENUMBER_DB_FORMAT = "E164"  # E.164 is standard: +***********

# settings.py
AUTH_USER_MODEL = "member.Member"
# --- END - 2FA Settings --- #

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # Required by django-otp
    "django_otp",
    "django_otp.plugins.otp_static",
    "django_otp.plugins.otp_totp",
    # Two-factor auth
    "two_factor",
    "two_factor.plugins.phonenumber",  # Optional, if using SMS
    "phonenumber_field",  # Optional dependency for phone number support
    # Django admin 2FA support
    "django.contrib.sites",
    # API Structure
    "rest_framework",
    # React Assistance
    "corsheaders",
    # Async Options
    "channels",
    # Celery & Task Management
    "django_celery_beat",
    # HTML Builder & Styling
    "crispy_forms",
    "crispy_tailwind",
    "tailwind",
    "apps.theme",
    "ckeditor",
    # Core Configuration (must be before custom apps for signal registration)
    "config.apps.ConfigConfig",
    # Custom Apps
    # 'tenant',
    "apps.testapp",
    "apps.member",
    "apps.customer",
    # API Endpoints
    "apps.api",
]

SITE_ID = 1

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",  # Must be first
    "config.middleware.CorrelationIdMiddleware",  # Add correlation IDs
    # 'tenant.middleware.TenantMiddleware',  # Tenant middleware
    "django_otp.middleware.OTPMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "config.middleware.RequestLoggingMiddleware",  # Request/response logging
    "config.middleware.SecurityLoggingMiddleware",  # Security event logging
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "config.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR.parent / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "config.wsgi.application"


# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

AUTHENTICATION_BACKENDS = ("django.contrib.auth.backends.ModelBackend",)

PASSWORD_HASHERS = [
    "django.contrib.auth.hashers.Argon2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2PasswordHasher",  # Fallbacks
    "django.contrib.auth.hashers.PBKDF2SHA1PasswordHasher",
    "django.contrib.auth.hashers.BCryptSHA256PasswordHasher",
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = "en-us"
TIME_ZONE = "UTC"
USE_I18N = True
USE_TZ = True

# Static files (CSS, JS, images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/
STATIC_URL = "static/"
STATICFILES_DIRS = [BASE_DIR.parent / "static"]  # Global static files
STATIC_ROOT = BASE_DIR.parent / "staticfiles"  # For `collectstatic` (prod)

# Media files (uploaded content)
MEDIA_URL = "/media/"
MEDIA_ROOT = BASE_DIR.parent / "media"

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# --- Logging Configuration --- #
from config.logging_settings import LOGGING

# --- REST API Details --- #
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",  # optional
    ),
    "DEFAULT_PERMISSION_CLASSES": ("rest_framework.permissions.IsAuthenticated",),
    "DEFAULT_RENDERER_CLASSES": ("rest_framework.renderers.JSONRenderer",),
}
# --- END - REST API Details --- #

# --- Cors Options --- #
# Note: CORS configuration is handled later in the file with environment-based settings
# --- END - Cors Options --- #

# --- Redis + Channels Options --- #
ASGI_APPLICATION = "config.asgi.application"

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [
                ("redis", 6379)
            ],  # 'redis' matches your docker-compose service name
        },
    },
}
# --- END - Redis + Channels Options --- #

# --- END - Redis + Celery Options --- #
CELERY_BROKER_URL = "redis://redis:6379/0"
CELERY_ACCEPT_CONTENT = ["json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_BACKEND = "redis://redis:6379/0"
# --- END - Redis + Celery Options --- #

# Crispy Forms Configuration
CRISPY_ALLOWED_TEMPLATE_PACKS = "tailwind"
CRISPY_TEMPLATE_PACK = "tailwind"

# Tailwind Configuration
TAILWIND_APP_NAME = "theme"

# CKEditor Configuration
CKEDITOR_CONFIGS = {
    "default": {
        "toolbar": "Custom",
        "toolbar_Custom": [
            ["Bold", "Italic", "Underline", "Strike"],
            ["NumberedList", "BulletedList", "-", "Outdent", "Indent"],
            ["JustifyLeft", "JustifyCenter", "JustifyRight", "JustifyBlock"],
            ["Link", "Unlink"],
            ["RemoveFormat", "Source"],
            ["Format", "Font", "FontSize"],
            ["TextColor", "BGColor"],
            ["Table", "HorizontalRule", "SpecialChar"],
            ["Image", "Flash"],
            ["Maximize"],
        ],
        "height": 300,
        "width": "100%",
        "removePlugins": "stylesheetparser",
        "allowedContent": True,
    },
    "simple": {
        "toolbar": "Basic",
        "toolbar_Basic": [
            ["Bold", "Italic", "Underline"],
            ["NumberedList", "BulletedList"],
            ["Link", "Unlink"],
            ["RemoveFormat"],
        ],
        "height": 200,
        "width": "100%",
    },
    "full": {
        "toolbar": "Full",
        "toolbar_Full": [
            [
                "Styles",
                "Format",
                "Bold",
                "Italic",
                "Underline",
                "Strike",
                "SpellChecker",
                "Undo",
                "Redo",
            ],
            ["Link", "Unlink", "Anchor"],
            ["Image", "Flash", "Table", "HorizontalRule"],
            ["TextColor", "BGColor"],
            ["Smiley", "SpecialChar"],
            ["Source"],
            ["JustifyLeft", "JustifyCenter", "JustifyRight", "JustifyBlock"],
            ["NumberedList", "BulletedList"],
            ["Indent", "Outdent"],
            ["Maximize"],
        ],
        "height": 400,
        "width": "100%",
    },
}

CKEDITOR_UPLOAD_PATH = "uploads/"
CKEDITOR_IMAGE_BACKEND = "pillow"

# Django Admin Customization
SITE_HEADER = "XD Incentives Admin"
SITE_TITLE = "XD Incentives Admin"
INDEX_TITLE = "Welcome to XD Incentives Administration"

# --- Email Configuration --- #
# Control whether emails are actually sent or just returned in API responses
SEND_EMAILS = env("SEND_EMAILS", default=False, cast=bool)

# Email backend configuration
if SEND_EMAILS:
    # Production email settings (configure these in your .env file)
    EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
    EMAIL_HOST = env("EMAIL_HOST", default="smtp.gmail.com")
    EMAIL_PORT = env("EMAIL_PORT", default=587, cast=int)
    EMAIL_USE_TLS = env("EMAIL_USE_TLS", default=True, cast=bool)
    EMAIL_HOST_USER = env("EMAIL_HOST_USER", default="")
    EMAIL_HOST_PASSWORD = env("EMAIL_HOST_PASSWORD", default="")
    DEFAULT_FROM_EMAIL = env("DEFAULT_FROM_EMAIL", default="<EMAIL>")
else:
    # Development: emails are logged to console and returned in API responses
    EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"
    DEFAULT_FROM_EMAIL = "<EMAIL>"

# Password reset email settings
PASSWORD_RESET_EMAIL_SUBJECT = env(
    "PASSWORD_RESET_EMAIL_SUBJECT", default="Password Reset Request - XD Incentives"
)
PASSWORD_RESET_EMAIL_TEMPLATE = env(
    "PASSWORD_RESET_EMAIL_TEMPLATE", default="emails/password_reset.html"
)
PASSWORD_RESET_EMAIL_TEXT_TEMPLATE = env(
    "PASSWORD_RESET_EMAIL_TEXT_TEMPLATE", default="emails/password_reset.txt"
)

# Google Address Validation API Settings
GOOGLE_ADDRESS_VALIDATION_API_KEY = env("GOOGLE_ADDRESS_VALIDATION_API_KEY", default="")
GOOGLE_ADDRESS_VALIDATION_ENABLED = env(
    "GOOGLE_ADDRESS_VALIDATION_ENABLED", default=True, cast=bool
)
GOOGLE_ADDRESS_VALIDATION_URL = (
    "https://addressvalidation.googleapis.com/v1:validateAddress"
)
GOOGLE_ADDRESS_VALIDATION_TIMEOUT = env(
    "GOOGLE_ADDRESS_VALIDATION_TIMEOUT", default=10, cast=int
)

# --- END - Email Configuration --- #

# --- Clerk Configuration --- #
from .clerk_settings import *  # noqa: F403

# Add Clerk authentication backend
AUTHENTICATION_BACKENDS = (
    "apps.member.clerk_auth.ClerkAuthenticationBackend",
    "django.contrib.auth.backends.ModelBackend",
)

# Add Clerk middleware
MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",  # Must be first
    "apps.member.clerk_auth.ClerkMiddleware",  # Add Clerk middleware
    # 'tenant.middleware.TenantMiddleware',  # Tenant middleware
    "django_otp.middleware.OTPMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

# Update CORS settings for Clerk
# CORS Settings - restrict in production
if DEBUG:
    # Development CORS settings - also configurable via environment
    default_dev_origins = [
        "http://localhost:8000",  # React dev server default
        "http://localhost:3000",  # Common React dev server port
        "http://localhost:5173",  # Vite dev server port
        *CLERK_ALLOWED_ORIGINS,  # Add Clerk allowed origins  # noqa: F405
    ]
    CORS_ALLOWED_ORIGINS = env.list("CORS_ALLOWED_ORIGINS", default=default_dev_origins)
    CORS_ALLOW_ALL_ORIGINS = False
else:
    # Production CORS settings - must be configured via environment
    CORS_ALLOWED_ORIGINS = env.list("CORS_ALLOWED_ORIGINS", default=[])
    CORS_ALLOW_ALL_ORIGINS = False

CORS_ALLOW_CREDENTIALS = True

# --- END - Clerk Configuration --- #

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Security Headers and HTTPS Settings
if not DEBUG:
    # Force HTTPS in production
    SECURE_SSL_REDIRECT = True
    SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

    # HSTS Settings
    SECURE_HSTS_SECONDS = 31536000  # 1 year
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True

    # Content Security and Cookie Settings
    SECURE_CONTENT_TYPE_NOSNIFF = True
    # SECURE_BROWSER_XSS_FILTER removed - deprecated setting that can cause issues in modern browsers
    X_FRAME_OPTIONS = "DENY"

    # Cookie Security
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = "Strict"
    CSRF_COOKIE_SECURE = True
    CSRF_COOKIE_HTTPONLY = True
    CSRF_COOKIE_SAMESITE = "Strict"
else:
    # Development settings - less restrictive
    SECURE_SSL_REDIRECT = False
    SESSION_COOKIE_SECURE = False
    CSRF_COOKIE_SECURE = False

# Content Security Policy
CSP_DEFAULT_SRC = ["'self'"]
CSP_SCRIPT_SRC = ["'self'", "'unsafe-inline'", "'unsafe-eval'"]
CSP_STYLE_SRC = ["'self'", "'unsafe-inline'"]
CSP_IMG_SRC = ["'self'", "data:", "https:"]
CSP_FONT_SRC = ["'self'", "https:"]

# Session Security
SESSION_COOKIE_AGE = 3600  # 1 hour session timeout
SESSION_EXPIRE_AT_BROWSER_CLOSE = True
SESSION_SAVE_EVERY_REQUEST = True

# =============================================================================
# END SECURITY SETTINGS
# =============================================================================
