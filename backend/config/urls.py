# URL configuration for XD Incentives project
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path

from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from two_factor.urls import urlpatterns as tf_urls

# Customize admin site
admin.site.site_header = settings.SITE_HEADER
admin.site.site_title = settings.SITE_TITLE
admin.site.index_title = settings.INDEX_TITLE

urlpatterns = [
    path("", include(tf_urls)),
    path("", include("apps.testapp.urls")),
    path("api/", include("apps.api.urls")),
    path("member/", include("apps.member.urls")),
    path("customer/", include("apps.customer.urls")),
    path("ckeditor/", include("ckeditor_uploader.urls")),
    path("admin/", admin.site.urls),
]

# Add Django auth URLs but override logout
from django.contrib.auth import urls as auth_urls

from apps.member.views import django_logout

# Include all auth URLs except logout
auth_urlpatterns = [url for url in auth_urls.urlpatterns if url.name != "logout"]

urlpatterns += [
    path("account/", include((auth_urlpatterns, "auth"))),
    path("account/logout/", django_logout, name="logout"),
]

# Add custom logout handling for better UX
urlpatterns += [
    path("logout/", django_logout, name="logout_redirect"),
]

urlpatterns += [
    path("api/token/", TokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("api/token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
]

# Serve static and media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static("/staticfiles/", document_root=settings.STATIC_ROOT)
