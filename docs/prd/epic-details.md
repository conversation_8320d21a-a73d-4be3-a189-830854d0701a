# Epic Details

## Epic 1: Infrastructure & Platform Foundation (Revised)

**Epic Goal:** To establish the foundational, multi-tenant cloud infrastructure, a **containerized local development environment**, project scaffolding, and a continuous integration pipeline. This epic ensures that we have a stable, automated, and secure environment before any application features are built, delivering a simple health-check endpoint as a final validation of the core setup.

### Story 1.1: Project Initialization & Quality Tooling
**As a** developer, **I want** to initialize a new monorepo with integrated linters and pre-commit hooks, **so that** code quality standards are automatically enforced from the very first commit.

**Acceptance Criteria:**
1. A new Git repository is created and initialized.
2. A root `package.json` file is configured with npm workspaces for `frontend` and `backend`.
3. A Biome configuration is added for frontend linting, and **Black is configured for Python formatting**.
4. **<PERSON>sky and lint-staged are set up to create a pre-commit hook.**
5. The pre-commit hook automatically runs Biome and Black on staged files, preventing commits with formatting errors.

### Story 1.2: Containerized Local Development Environment
**As a** developer, **I want** a Docker-based local development environment, **so that** I can run the full application consistently across different machines, mirroring the production setup.

**Acceptance Criteria:**
1. A `docker-compose.yml` file is created at the project root.
2. The Docker Compose setup defines services for the frontend, backend, and a MySQL database.
3. The services are configured to use Docker volumes for persistent data and code, enabling hot-reloading for development.
4. The entire local environment can be started with a single `docker-compose up` command.

### Story 1.3: IaC Setup for Core AWS Services
**As a** DevOps engineer, **I want** to define the core cloud infrastructure using Terraform, **so that** I can automatically and reliably provision the necessary AWS services for staging and production.

**Acceptance Criteria:**
1. Terraform scripts are created to provision an isolated AWS environment (VPC, subnets).
2. The script successfully provisions a secure S3 bucket for file storage.
3. The script successfully provisions an Amazon RDS for MySQL instance for staging/production.

### Story 1.4: Backend Application Scaffolding
**As a** developer, **I want** to scaffold the initial Django backend application to run within the Docker environment, **so that** I have a foundational structure for building APIs.

**Acceptance Criteria:**
1. A new Django project is created within the `/backend` directory.
2. The backend service in `docker-compose.yml` is configured to correctly run the Django application.
3. The application is configured to connect to the MySQL Docker container for local development.
4. The Django development server starts successfully via `docker-compose up`.

### Story 1.5: Frontend Application Scaffolding with Storybook
**As a** developer, **I want** to scaffold the initial React frontend application and integrate Storybook, **so that** I can develop and showcase UI components in isolation.

**Acceptance Criteria:**
1. A new React with TypeScript project is created within the `/frontend` directory using Vite.
2. The frontend service in `docker-compose.yml` is configured to correctly run the Vite dev server.
3. **Storybook is installed and configured for the frontend application.**
4. A sample "Button" component is created and is viewable in Storybook when it is run.

### Story 1.6: Cypress Test Suite Setup
**As a** developer, **I want** to set up the Cypress E2E testing suite, **so that** we can write and run tests against critical user flows.

**Acceptance Criteria:**
1. Cypress is installed and configured for the frontend project.
2. Cypress is configured to run against the frontend application within the Docker environment.
3. A basic sample test is created that successfully visits the root page of the frontend application.
4. The test suite can be run locally using an npm script (e.g., `npm run cypress:open`).

### Story 1.7: CI/CD Pipeline with Automated Testing
**As a** developer, **I want** to set up a CI/CD pipeline that automatically runs linters and E2E tests, **so that** code quality and application health are verified on every pull request.

**Acceptance Criteria:**
1. A GitHub Actions workflow is configured to trigger on pull requests.
2. The pipeline installs all dependencies and builds both the frontend and backend.
3. The pipeline runs the Biome and Black linters and fails if issues are found.
4. **The CI pipeline successfully runs the Cypress E2E test suite against a preview environment and fails if tests do not pass.**

### Story 1.8: Health Check Endpoint & E2E Test
**As a** DevOps engineer, **I want** to create a health check endpoint and a corresponding E2E test, **so that** I can verify the entire stack is working and deployed correctly.

**Acceptance Criteria:**
1. A `/health` API endpoint is created in the Django application.
2. Making a GET request to the `/health` endpoint returns a JSON response of `{"status": "ok"}`.
3. The frontend application is updated to display the status from the `/health` endpoint on its main page.
4. A Cypress test is created that visits the main page and asserts that the "ok" status is displayed.
5. The full application is deployed to a staging environment via the CI/CD pipeline, and the health check test passes.

## Epic 2: Authentication & Security (Revised)

**Epic Goal:** To implement a robust and secure authentication and authorization system using a **hybrid ABAC/ReBAC model**. This includes integrating Clerk for user management, modeling the sales hierarchy and user attributes, enhancing JWTs with dynamic permissions, and creating a flexible API authorization layer that can enforce complex, context-aware rules.

### Story 2.1: Basic User Sign-Up and Sign-In
**As a** new user, **I want** to be able to sign up and sign in to the application, **so that** I can access the platform's features.

**Acceptance Criteria:**
1. The Clerk service is integrated into both the frontend and backend applications.
2. A basic Django user model is created and linked to Clerk user IDs.
3. The frontend application includes Clerk's pre-built UI components for sign-up and sign-in.
4. A user can successfully create a new account and sign in, receiving a valid JWT.
5. The backend is able to validate the JWT from Clerk.

### Story 2.2: Model User Attributes and Sales Hierarchy
**As a** system administrator, **I want** to define user attributes (like territory) and their reporting relationships (e.g., MSM manages MSRs), **so that** the system has the contextual data needed for dynamic authorization.

**Acceptance Criteria:**
1. The Django user model is extended to include key attributes such as `territory_id`.
2. A relationship model is created in Django to define the managerial hierarchy (e.g., `MSR_A` reports to `MSM_B`).
3. An admin interface or script is created to manage these attributes and relationships.
4. User attributes and relationships can be queried via their Clerk user ID.

### Story 2.3: Enhance JWTs with Dynamic Authorization Claims
**As a** developer, **I want** to use Clerk's webhooks or other mechanisms to enrich user JWTs with custom claims representing their attributes and relationships, **so that** the API has the necessary data to make authorization decisions without extra database calls.

**Acceptance Criteria:**
1. A mechanism (e.g., Django webhook receiver) is implemented to listen for Clerk session events.
2. Upon user sign-in, the system fetches the user's attributes (e.g., `territory_id`) and relationships (e.g., `manager_id`) from Django.
3. The fetched data is added to the user's session token as custom claims via the Clerk Backend API (e.g., `public_metadata`).
4. The resulting JWT, when decoded by the API, contains these authorization claims.

### Story 2.4: Implement a Dynamic API Authorization Middleware
**As a** developer, **I want** to create a reusable Django REST Framework permission class or middleware that can evaluate complex ABAC and ReBAC rules, **so that** we can secure endpoints with flexible, context-aware logic.

**Acceptance Criteria:**
1. A custom Django permission class is created that can be applied to API views.
2. The class can parse custom claims from the validated JWT.
3. The class can evaluate rules passed to it (e.g., "user must be the object's owner" or "user's territory must match the object's territory").
4. A test endpoint is protected by this new permission class, which denies access if the required rules are not met.

### Story 2.5: Implement Relationship-Based Access (ReBAC)
**As an** MSM, **I want** to view and manage claims submitted only by my direct reports, **so that** I can manage my team effectively without seeing irrelevant data.

**Acceptance Criteria:**
1. A `GET /api/claims` endpoint is created.
2. The endpoint is protected by the dynamic authorization middleware from Story 2.4.
3. The middleware rule checks if the requesting user is the manager of the user who created the claim (a relationship check).
4. When an MSM calls the endpoint, it returns only the claims submitted by MSRs who report to them.
5. An MSR calling the same endpoint can only see their own claims.

### Story 2.6: Implement Attribute-Based Access (ABAC)
**As a** TSM, **I want** to be able to approve new customers only within my assigned territory, **so that** data integrity is maintained and I can only act on relevant entities.

**Acceptance Criteria:**
1. An endpoint `POST /api/customers/{id}/approve` is created.
2. The endpoint is protected by the dynamic authorization middleware, configured with an attribute-based rule.
3. The rule verifies that the `territory_id` claim in the TSM's JWT matches the `territory_id` attribute of the customer record.
4. A TSM can successfully approve a customer in their own territory.
5. A TSM attempting to approve a customer in a different territory receives a 403 Forbidden error.

## Epic 3: Customer & Claim Management Foundation

**Epic Goal:** To develop the essential components for managing customer data and enabling sales representatives (MSRs) to submit new claims. This epic includes creating the core data models, building the user interfaces for submission, and implementing the critical TSM approval workflow to ensure data integrity.

### Story 3.1: Create Customer Data Model
**As a** developer, **I want** to define and create the `Customer` data model in the backend, **so that** we have a database structure to store all customer-related information.

**Acceptance Criteria:**
1. A `Customer` model is created in the Django backend.
2. The model includes fields for name, address, status (e.g., 'Pending', 'Approved', 'Denied'), and the territory it belongs to.
3. The model is linked to the tenant to ensure multi-tenant data isolation.
4. The database migration is successfully created and applied.

### Story 3.2: Implement New Customer Submission Form
**As an** MSR, **I want** a simple form to submit a new potential customer for approval, **so that** I can get them into the system to be eligible for incentive programs.

**Acceptance Criteria:**
1. A new page/form is created in the frontend for submitting new customers.
2. Upon submission, the form sends the new customer's data to a `POST /api/customers` endpoint.
3. A new customer record is created in the database with a default status of 'Pending'.
4. The MSR who submitted the customer is recorded as the owner of the record.

### Story 3.3: Implement TSM Customer Approval Workflow
**As a** TSM, **I want** to see a queue of pending customers in my territory and be able to approve or deny them, **so that** I can maintain the integrity of our customer data.

**Acceptance Criteria:**
1. An API endpoint `GET /api/pending-customers` is created.
2. Using the ABAC/ReBAC middleware from Epic 2, the endpoint only returns pending customers whose territory matches the TSM's territory.
3. The frontend includes a "Pending Customers" page for TSMs that displays this list.
4. The TSM can click "Approve" or "Deny" on a customer, which calls the API to update the customer's status accordingly.

### Story 3.4: Integrate Address Validation Service
**As a** user submitting a new customer, **I want** the address to be validated in real-time, **so that** I can correct errors and ensure data accuracy from the start.

**Acceptance Criteria:**
1. The customer submission form is integrated with an address validation service (e.g., UPS, SmartyStreets).
2. As the user types an address, the system provides validation feedback.
3. If an address is invalid or ambiguous, the user is presented with suggestions for correction.
4. Only validated, standardized addresses are saved to the database.

### Story 3.5: Create Claim Data Model
**As a** developer, **I want** to define and create the `Claim` data model in the backend, **so that** we have a database structure to store all claim-related information.

**Acceptance Criteria:**
1. A `Claim` model is created in the Django backend.
2. The model includes fields for status (e.g., 'Submitted', 'Approved'), amount, and relationships to the `User` (MSR) and `Customer` models.
3. The model includes a field to store the URL of the uploaded invoice image in S3.
4. The database migration is successfully created and applied.

### Story 3.6: MSR Claim Submission UI
**As an** MSR, **I want** a streamlined, multi-step form to submit a new sales claim, **so that** I can quickly and accurately report my sales for incentive payouts.

**Acceptance Criteria:**
1. A "New Claim" page is created in the frontend application.
2. The form allows the MSR to select one of their approved customers.
3. The form includes an interface to upload an invoice image, which is successfully sent to the S3 bucket provisioned in Epic 1.
4. Upon submission, a new `Claim` record is created in the database with a default status of 'Submitted'.

## Epic 4: Campaign Engine

**Epic Goal:** To design and build a flexible, rule-based campaign engine capable of handling all of the pilot customer's complex incentive structures without requiring new code for new campaigns. This epic will deliver the backend logic and data models necessary to calculate payouts, manage contests, and apply various caps and tiers automatically.

### Story 4.1: Campaign & Rule Data Models
**As a** developer, **I want** to create the core database models for `Campaigns`, `Rules`, and `Tiers`, **so that** we can store and manage the complex logic of various incentive programs.

**Acceptance Criteria:**
1. A `Campaign` model is created in Django, including fields for name, start/end dates, and the associated tenant.
2. A `Rule` model is created that can be associated with a Campaign and can define different payout structures (e.g., percentage, points).
3. The data models support defining eligibility criteria, such as specific products or customer types.
4. The models are flexible enough to support both individual caps (per user/customer) and campaign-wide budget limits.
5. Database migrations are successfully created and applied.

### Story 4.2: Admin UI for Basic Campaign Creation
**As an** administrator, **I want** a basic user interface to create and manage the lifecycle of a campaign, **so that** I can set up new incentive programs.

**Acceptance Criteria:**
1. A new "Campaigns" section is added to the administrative interface.
2. An administrator can create a new campaign, providing a name, description, and effective start/end dates.
3. The list of all campaigns is viewable, showing their status (e.g., Draft, Active, Expired).
4. An administrator can edit the basic details of a draft campaign.

### Story 4.3: Implement Product-Based Payout Rule
**As an** administrator, **I want** to add a rule to a campaign that defines a specific payout percentage for a given product, **so that** I can create basic "sell this, get that" incentives.

**Acceptance Criteria:**
1. The campaign admin UI allows adding a "Product Payout" rule to a campaign.
2. The rule configuration allows selecting a product and specifying a numeric payout value (e.g., 5%).
3. A backend service is created that, given a claim, can calculate the correct payout based on this simple rule.
4. The calculation is logged with a reference to the specific campaign and rule that generated the payout.

### Story 4.4: Implement Tiered & Capped Payout Logic
**As an** administrator, **I want** to add tiers and caps to my payout rules, **so that** I can run the specific Citgo and RTTC campaigns which have time-based tiers and user- or customer-level earning limits.

**Acceptance Criteria:**
1. The rule configuration UI is enhanced to allow defining tiers (e.g., based on customer's tenure: Year 1, Year 2).
2. The UI allows setting a monetary cap on a rule (e.g., $35,000 per year per MSR).
3. The payout calculation engine correctly identifies the appropriate tier for a given claim.
4. The engine tracks cumulative payouts against a cap and stops generating new payouts once the cap is reached.

### Story 4.5: Implement Manager Payout Rule
**As an** administrator, **I want** to create a rule that pays a manager a percentage of their direct reports' earnings, **so that** managers are incentivized to coach their teams effectively.

**Acceptance Criteria:**
1. A "Manager Payout" rule type can be added to a campaign in the admin UI, specifying a percentage (e.g., 20%).
2. The calculation engine correctly identifies an MSR's manager based on the hierarchy defined in Epic 2.
3. When an MSR earns a payout from an eligible campaign, a corresponding manager payout is calculated and linked to their manager.
4. Manager payouts do not have a cap unless explicitly configured.

### Story 4.6: Implement Points-Based Contest Logic & Leaderboard
**As an** administrator, **I want** to create a contest-style campaign that awards points for selling specific combinations of products, **so that** we can run engaging contests like the "Citgo HD Contest".

**Acceptance Criteria:**
1. A "Points Contest" campaign type can be created in the admin UI.
2. The rules can be configured to award points for specific products or combinations of products.
3. The calculation engine correctly awards points to users based on their approved claims.
4. A new API endpoint (`GET /api/leaderboards/{campaign_id}`) is created that returns a sorted list of participants and their total points in real-time.

## Epic 5: Claim Review & Approval

**Epic Goal:** To empower Sales Managers (MSMs) with an efficient dashboard to review and process claims submitted by their direct reports. This epic is critical for ensuring the accuracy of payouts, preventing fraud, and maintaining a transparent and timely approval process.

### Story 5.1: MSM Claim Queue API
**As a** backend developer, **I want** to create a secure API endpoint that provides an MSM with a list of all pending claims from their direct reports, **so that** the frontend has the necessary data to build the review dashboard.

**Acceptance Criteria:**
1. A new `GET /api/claims/queue` endpoint is created in the Django application.
2. The endpoint is protected by the ReBAC authorization middleware.
3. The endpoint returns only claims with a status of 'Submitted'.
4. The data returned is limited to claims submitted by MSRs who report directly to the authenticated MSM.
5. An MSM with no pending claims receives an empty list.

### Story 5.2: MSM Claim Review Dashboard UI
**As an** MSM, **I want** a clear and organized dashboard that displays all pending claims from my team, **so that** I can quickly see what needs my attention.

**Acceptance Criteria:**
1. A new "Claim Review" page is created in the frontend application, accessible to users with the MSM role.
2. On page load, the new API endpoint (`/api/claims/queue`) is called to fetch and display the list of pending claims.
3. The dashboard displays key information for each claim, such as submission date, MSR name, customer name, and claim amount.
4. The list of claims can be sorted and filtered by MSR or submission date.

### Story 5.3: Single Claim Detail View and Actions
**As an** MSM, **I want** to be able to click on a single claim to view all its details, including the attached invoice, **so that** I can make an informed decision to approve or deny it.

**Acceptance Criteria:**
1. Clicking on a claim in the dashboard navigates to a detailed view for that specific claim.
2. The detail view displays all claim information and a view of the uploaded invoice image.
3. "Approve" and "Deny" buttons are present and functional.
4. Clicking "Approve" calls a `POST /api/claims/{id}/approve` endpoint, updating the claim's status to 'Approved'.
5. The approved claim is removed from the MSM's pending queue.

### Story 5.4: Implement Batch Claim Operations
**As an** MSM, **I want** to select multiple claims from my queue and approve or deny them all at once, **so that** I can process my team's claims more efficiently.

**Acceptance Criteria:**
1. The claim review dashboard UI includes a checkbox next to each claim in the list.
2. "Approve Selected" and "Deny Selected" buttons are available on the dashboard.
3. When an MSM selects multiple claims and clicks "Approve Selected", a single API call is made with the IDs of all selected claims.
4. The backend successfully updates the status of all specified claims to 'Approved'.
5. All approved claims are removed from the pending queue.

### Story 5.5: Capture Claim Denial Reasons
**As an** MSM, **I want** to provide a reason when I deny a claim, **so that** the MSR understands why the claim was rejected and what they need to do to correct it.

**Acceptance Criteria:**
1. When an MSM denies a claim (either single or in a batch), a modal appears prompting for a denial reason.
2. The modal contains a dropdown of common denial reasons (e.g., 'Duplicate Invoice', 'Incorrect Information') and an option for a custom text entry.
3. The selected reason is saved to the claim record in the database.
4. The claim's status is updated to 'Denied'.
5. The MSR is notified of the denial and the reason provided.

## Epic 6: Fund Management & Payout Calculation

**Epic Goal:** To automate the entire financial lifecycle of a claim, from calculating the final payout based on complex campaign rules to providing administrators with the tools for budget management, tax compliance, and dispute resolution. This epic ensures that partners are paid accurately and transparently.

### Story 6.1: Payout Calculation Service
**As a** system, **I want** to automatically process all approved claims against the campaign engine, **so that** accurate, auditable payout records are created without manual intervention.

**Acceptance Criteria:**
1. A backend service is created that processes claims with the status 'Approved'.
2. The service correctly applies all relevant campaign rules (from Epic 4) to each claim to determine the final monetary or point value.
3. For each successful calculation, an immutable `Payout` record is created in the database, linked to the claim and the user.
4. After processing, the original claim's status is updated to 'Processed' to prevent duplicate calculations.

### Story 6.2: Implement Fund & Budget Management
**As an** administrator, **I want** to create budgets and associate them with specific campaigns, **so that** I can track spending in real-time and prevent cost overruns.

**Acceptance Criteria:**
1. An admin interface is created to define funds/budgets and link them to one or more campaigns.
2. When the Payout Calculation Service runs, the value of each new payout is decremented from the associated campaign budget.
3. The system prevents payouts from being generated if a campaign's budget is insufficient.
4. Alerts are configured to notify administrators when a campaign budget falls below a defined threshold (e.g., 10%).

### Story 6.3: Implement W9 Tax Document Collection
**As a** partner (MSR), **I want** to securely submit my W9 tax form through the platform, **so that** I am eligible to receive payments and the company can meet its tax reporting obligations.

**Acceptance Criteria:**
1. A secure form is created within the user's profile page for uploading a W9 document.
2. The uploaded file is stored securely in a private S3 bucket with restricted access.
3. An administrative interface is created for finance teams to review submitted W9s and mark them as 'Verified'.
4. The system prevents payouts for a user from being included in a payment export until their W9 status is 'Verified'.

### Story 6.4: Payout Export for Finance Teams
**As a** finance administrator, **I want** to export a report of all pending payouts, **so that** I can process payments through our external payroll system.

**Acceptance Criteria:**
1. An admin page is created to generate a CSV export of all `Payout` records that are ready for payment.
2. The export only includes payouts for users with a 'Verified' W9 status.
3. The CSV file contains all necessary details for payment processing (e.g., User Name, Payout Amount, User ID).
4. After a batch is successfully exported, the corresponding `Payout` records are updated to a 'Paid' status.

### Story 6.5: User-Facing Dispute Submission
**As an** MSR, **I want** to be able to dispute a claim or payout if I believe there is an error, **so that** I can ensure I am being compensated fairly.

**Acceptance Criteria:**
1. A "Report an Issue" or "Dispute" button is available on the claim detail and payout history pages.
2. Clicking the button opens a form where the user can describe the issue.
3. Submitting the form creates a new `Dispute` ticket in the system, linked to the relevant user and claim/payout.
4. The user receives a confirmation that their dispute has been submitted.

### Story 6.6: Admin Dispute Resolution UI
**As an** administrator, **I want** a dashboard to view and manage all open disputes, **so that** I can investigate and resolve partner issues in under 24 hours.

**Acceptance Criteria:**
1. An admin dashboard is created that lists all `Dispute` tickets with a status of 'Open'.
2. Clicking on a dispute shows the user's description, a link to the associated claim/payout, and a history of actions taken.
3. The administrator can add internal notes, change the dispute status (e.g., 'Under Review', 'Resolved'), and communicate with the user.
4. If a correction is needed, the administrator has the ability to generate a manual credit or adjustment payout.

## Epic 7: MSR Dashboard & Portal

**Epic Goal:** To provide the MSR with a transparent, mobile-first self-service portal to track their performance, earnings, and claim status in real-time. This epic directly addresses the core user pain point of having no visibility into incentive programs, aiming to deliver an experience that is both informative and motivating.

### Story 7.1: MSR Dashboard Layout & Navigation
**As an** MSR, **I want** a clean, mobile-friendly dashboard when I log in, **so that** I can get a quick overview of my incentive-related activities at a glance.

**Acceptance Criteria:**
1. After a successful login, users with the MSR role are directed to a dedicated dashboard page.
2. The dashboard layout is fully responsive, adhering to a mobile-first design philosophy.
3. The layout includes a primary navigation menu and defined sections for key data widgets (e.g., Earnings, Recent Claims).
4. The dashboard and its components load quickly, meeting the performance NFRs.

### Story 7.2: Real-Time Earnings Widget
**As an** MSR, **I want** to see my total pending and paid earnings prominently on my dashboard, **so that** I have immediate visibility into the financial impact of my sales.

**Acceptance Criteria:**
1. A new API endpoint (`GET /api/me/earnings`) is created to aggregate a user's total pending and paid-out earnings for the current fiscal period.
2. The dashboard includes a widget that calls this endpoint and clearly displays the two totals, formatted as currency.
3. The data reflects all processed payouts and is updated in near real-time.

### Story 7.3: Claim History & Status List
**As an** MSR, **I want** to see a list of my recently submitted claims and their current status on my dashboard, **so that** I can track their progress through the approval and payment process.

**Acceptance Criteria:**
1. A new API endpoint (`GET /api/me/claims`) is created to fetch the current user's claims.
2. The dashboard displays a list of the MSR's most recent claims.
3. Each item in the list clearly shows the claim's status (e.g., 'Submitted', 'Approved', 'Denied', 'Paid') using a visual indicator like a colored badge.
4. Clicking on a claim in the list navigates to its detailed view.

### Story 7.4: Campaign Progress Visualization
**As an** MSR, **I want** to see my progress within active campaigns I'm participating in, **so that** I can understand how close I am to reaching goals or yearly caps.

**Acceptance Criteria:**
1. A new API endpoint (`GET /api/me/campaign-progress`) is created that returns the MSR's progress for active, capped campaigns.
2. The dashboard features a widget that displays this information visually, for instance, with a progress bar.
3. The visualization clearly shows the MSR's current earnings against the total cap for relevant campaigns (e.g., "$5,000 / $35,000 earned").

### Story 7.5: MSR Profile & Tax Document Page
**As an** MSR, **I want** a dedicated profile page where I can manage my account details and tax documents, **so that** I can ensure my information is up-to-date for payment processing.

**Acceptance Criteria:**
1. A "My Profile" page is created and is accessible from the main navigation.
2. The page displays the user's account information.
3. The W9 upload form (from Story 6.3) is included on this page.
4. The page clearly displays the current status of their W9 document ('Not Submitted', 'Pending Verification', 'Verified').

## Epic 8: Manager Dashboards (MSM, TSM, NSM)

**Epic Goal:** To provide each level of sales management with a tailored, data-driven dashboard that gives them the visibility and tools required to manage their teams, territories, and national programs effectively. This epic translates raw data into actionable insights for leadership.

### Story 8.1: MSM Team Performance Dashboard
**As an** MSM, **I want** a dashboard that consolidates my claim review queue with key performance indicators for my team, **so that** I can manage my daily tasks and monitor my team's performance from a single screen.

**Acceptance Criteria:**
1. When a user with the MSM role logs in, they are directed to a dedicated dashboard.
2. The dashboard prominently features the Claim Review queue component built in Epic 5.
3. The dashboard includes widgets displaying team-level KPIs, such as "Total Claims Submitted (Month-to-Date)" and "Team Approval Rate".
4. A simple leaderboard is displayed showing the top-performing MSRs within the MSM's team.

### Story 8.2: TSM Territory Oversight Dashboard
**As a** TSM, **I want** a dashboard that provides a high-level overview of my territory's performance and highlights pending administrative tasks, **so that** I can effectively oversee my region.

**Acceptance Criteria:**
1. Users with the TSM role are directed to a territory-specific dashboard upon login.
2. The dashboard includes the Customer Approval queue component built in Epic 3.
3. The dashboard displays territory-wide KPIs, such as "Total Sales Volume vs. Quota" and "Campaign Participation Rate".
4. The data displayed is aggregated from all MSMs and MSRs within the TSM's assigned territory.

### Story 8.3: TSM Comparative Analytics
**As a** TSM, **I want** to be able to compare my territory's performance against other territories, **so that** I can identify areas of excellence and opportunities for improvement.

**Acceptance Criteria:**
1. A new "Comparative Analytics" page is created, accessible to TSMs.
2. The page features a report or chart that compares the TSM's territory against the anonymized average of other territories on key metrics (e.g., Sales Volume, Program ROI).
3. A new API endpoint is created to provide this aggregated and anonymized data.

### Story 8.4: NSM National KPI Dashboard
**As an** NSM, **I want** an executive-level dashboard with a clear, concise view of national performance metrics, **so that** I can make strategic decisions without getting lost in operational details.

**Acceptance Criteria:**
1. Users with the NSM role are directed to a high-level national dashboard upon login.
2. The dashboard displays a small number of critical, nationwide KPIs, such as "Total Incentive Spend vs. Budget", "Total Channel Revenue", and "Overall Partner Participation Rate".
3. All data is aggregated from across all territories.
4. The interface is clean, visually appealing, and easy to interpret in seconds.

### Story 8.5: NSM Performance Drill-Down
**As an** NSM, **I want** the ability to drill down into the national KPIs, **so that** I can investigate trends and compare performance across territories when needed.

**Acceptance Criteria:**
1. The KPI widgets on the NSM dashboard are interactive.
2. Clicking on a KPI (e.g., "Total Channel Revenue") navigates the NSM to a new page showing a detailed breakdown of that metric.
3. The detail view allows the NSM to see the metric broken down by territory, sorted from highest to lowest performance.

## Epic 9: Communications & Content

**Epic Goal:** To build a robust communication framework that keeps users informed through automated, event-driven notifications (both in-app and via email), and to empower administrators with the ability to manage static content like FAQs and policies without developer intervention.

### Story 9.1: Email Service Integration
**As a** developer, **I want** to integrate the backend with a reliable email service (AWS SES), **so that** the platform can send automated transactional emails.

**Acceptance Criteria:**
1. The Django application is successfully configured to send emails using AWS SES.
2. Configuration includes handling for bounced emails and complaints.
3. A basic email template with tenant-specific branding (logo) is created.
4. A test email can be successfully sent and received from the application.

### Story 9.2: Implement Transactional Emails for Claims
**As an** MSR, **I want** to receive an email notification when my claim is approved or denied, **so that** I am kept up-to-date on the status of my submissions.

**Acceptance Criteria:**
1. When an MSM approves a claim, an automated email is sent to the submitting MSR.
2. When an MSM denies a claim, an automated email is sent to the MSR, and the email includes the reason for the denial.
3. The emails use the branded template from the previous story.

### Story 9.3: Real-Time In-App Notification System
**As a** user, **I want** to receive real-time notifications within the app for important events, **so that** I am immediately aware of updates without having to check my email or refresh the page.

**Acceptance Criteria:**
1. A notification icon with an unread-count badge is added to the main application header.
2. The backend is configured to push a real-time message (e.g., via WebSockets) when a user's claim is approved.
3. The frontend client receives this message and displays a notification to the user in real-time.
4. The unread-count badge increments accordingly.
5. Clicking the notification icon displays a list of recent, read, and unread notifications.

### Story 9.4: Static Content Management System (CMS)
**As an** administrator, **I want** to be able to create and edit informational pages like "FAQ" or "Privacy Policy", **so that** I can update important content for users without needing a new software release.

**Acceptance Criteria:**
1. A "Content Pages" section is added to the administrative UI.
2. An administrator can create a new page, define its URL path (e.g., `/faq`), and add content using a WYSIWYG editor.
3. The administrator can save pages in a 'Draft' state or 'Publish' them to make them live.
4. A history of changes for each page is maintained.

### Story 9.5: Display Static Content in Frontend
**As a** user, **I want** to be able to access informational pages like "FAQ" or "Terms of Service" from the main application, **so that** I can find answers to my questions and understand the platform's policies.

**Acceptance criteria:**
1. Links to published content pages (e.g., in the application footer) are made available.
2. When a user navigates to a static page's URL (e.g., `/faq`), the frontend fetches the content from the CMS and renders it within the standard application layout.
3. The content displays correctly as formatted in the WYSIWYG editor.

## Epic 10: Core Reporting & Analytics

**Epic Goal:** To provide users with a suite of standard operational reports for analyzing sales performance and payouts. This epic ensures that all personas can access the data they need for decision-making and have the ability to export it for offline use, fulfilling a key requirement for business intelligence.

### Story 10.1: Reporting API Foundation
**As a** backend developer, **I want** to create an efficient, secure, and filterable API endpoint for generating report data, **so that** the frontend can request different datasets without requiring a unique endpoint for every report.

**Acceptance Criteria:**
1. A generic API endpoint (e.g., `POST /api/reports/{report_name}`) is created that can handle requests for different report types.
2. The endpoint accepts parameters for date ranges and filtering (e.g., by user, by territory).
3. The endpoint is protected by the authorization middleware to ensure users can only request data they are permitted to see.
4. The backend uses optimized queries to ensure report generation does not negatively impact overall application performance.

### Story 10.2: Sales Performance Report
**As a** manager (MSM/TSM), **I want** to view a sales performance report for my team or territory, **so that** I can track progress against goals and identify top performers.

**Acceptance Criteria:**
1. A "Reports" section is added to the application's main navigation.
2. A "Sales Performance" report is available that allows the manager to select a date range.
3. The report displays a summary of sales data (e.g., total volume, number of claims) for their scope of responsibility (team or territory).
4. The report includes a breakdown of performance by individual MSR.

### Story 10.3: Payouts Report
**As a** user (MSR/MSM/TSM), **I want** to view a detailed report of historical payouts, **so that** I can verify my earnings and understand how commissions were calculated.

**Acceptance Criteria:**
1. A "Payouts Report" is available in the Reports section.
2. The report allows users to select a date range to view all payouts they have permission to see.
3. Each line item in the report details a single payout, including the date, amount, and a link to the originating claim.
4. An MSR can only see their own payouts; managers can see payouts for their entire team/territory.

### Story 10.4: Report Export to CSV
**As a** user, **I want** to be able to export any report to a CSV file, **so that** I can perform my own analysis or import the data into other systems.

**Acceptance Criteria:**
1. Every report page in the UI contains an "Export to CSV" button.
2. Clicking the button downloads a CSV file containing the complete dataset for the report, including all applied filters.
3. The export function works efficiently even for large datasets (e.g., thousands of rows).

### Story 10.5: Role-Based Access to Reports
**As a** developer, **I want** to ensure all reports adhere to the platform's hierarchical permission model, **so that** users can never access data outside their scope of authority.

**Acceptance Criteria:**
1. The authorization middleware (from Epic 2) is applied to all reporting functionality.
2. The list of available reports is filtered based on the user's role.
3. All data returned by the reporting API is strictly filtered based on the user's position in the sales hierarchy (e.g., an MSM cannot see data from another MSM's team).

## Epic 11: Data Management Tools (Revised)

**Epic Goal:** To provide administrators with the tools to perform bulk data management for **users**, customers, and claims through CSV imports. This epic is critical for the MVP's operational viability, enabling the initial data load and ongoing bulk updates until full enterprise integrations are developed in a later phase.

### Story 11.1: CSV Parsing & Validation Service
**As a** backend developer, **I want** to build a generic service that can parse and validate CSV files against predefined templates, **so that** we have a reliable and reusable foundation for all data import features.

**Acceptance Criteria:**
1. A backend service is created that accepts a CSV file and an import type (e.g., 'users', 'customers', 'claims').
2. The service validates the CSV's headers against the expected template for that import type.
3. The service validates each row for correct data types, required fields, and basic business rules.
4. The service returns a structured result, separating valid rows from invalid rows, with specific error messages for each invalid row.

### Story 11.2: Admin UI for CSV Import
**As an** administrator, **I want** a simple user interface to upload CSV files for data imports, **so that** I can easily manage bulk data.

**Acceptance Criteria:**
1. A "Data Import" page is created within the administrative section of the application.
2. The UI allows the administrator to select the type of data they are importing (e.g., 'Users', 'Customers', 'Claims').
3. For each import type, a link is provided to download a blank CSV template with the correct headers.
4. The UI includes a file uploader that sends the selected file to the backend for processing.

### Story 11.3: Implement User Data Import
**As an** administrator, **I want** to import a list of users with their roles and manager assignments from a CSV file, **so that** I can efficiently provision and structure entire teams.

**Acceptance Criteria:**
1. The 'Users' option in the import UI is functional.
2. The backend validates the user CSV, ensuring required fields (e.g., email, name, role) are present and valid.
3. The import process creates new user records via the Clerk integration.
4. The user's role, territory, and manager relationship (based on manager's email) are correctly established in the Django database.

### Story 11.4: Implement Customer Data Import
**As an** administrator, **I want** to be able to import a list of new customers from a CSV file, **so that** I can perform bulk customer setup efficiently.

**Acceptance Criteria:**
1. The 'Customers' option in the import UI is functional.
2. For each valid row in the CSV, a new `Customer` record is created in the database.
3. The import process correctly associates the new customers with the correct tenant.

### Story 11.5: Implement Sales Claim Data Import
**As an** administrator, **I want** to import a batch of sales claims from a CSV file, **so that** I can handle bulk submissions or historical data loads.

**Acceptance Criteria:**
1. The 'Claims' option in the import UI is functional.
2. The backend correctly validates the claim data, ensuring the specified MSR and Customer exist.
3. For each valid row, a new `Claim` record is created with a default status of 'Submitted'.

### Story 11.6: Import Error Handling & Reporting
**As an** administrator, **I want** to receive a clear summary and a detailed error report when my CSV import has issues, **so that** I can quickly identify, correct, and re-upload the failed records.

**Acceptance Criteria:**
1. After a CSV file is processed, the UI displays a summary (e.g., "98 rows succeeded, 2 rows failed").
2. A link is provided to download a new CSV file containing only the rows that failed.
3. This error report CSV includes all the original data from the failed rows, plus a new final column explaining the specific validation error for each row.

## Epic 12: Historical Data Migration

**Epic Goal:** To safely, accurately, and efficiently migrate nine years of historical user and transaction data from the existing Django/MySQL system into the new platform. This epic ensures that no data is lost, business continuity is maintained, and all historical records are available from day one of the new system's launch.

### Story 12.1: Data Mapping & Transformation Logic
**As a** data engineer, **I want** to analyze the legacy database schema and write the transformation logic to map it to the new platform's schema, **so that** we have a clear and accurate plan for data conversion.

**Acceptance Criteria:**
1. A data mapping document is created that details the source table/column for every field in the new `User`, `Customer`, `Claim`, and `Payout` models.
2. A set of Python scripts or modules is created to handle the data transformation logic (e.g., reformatting dates, mapping old status values to new ones).
3. The logic accounts for potential null values or inconsistencies in the legacy data.

### Story 12.2: Develop User Migration Script
**As a** data engineer, **I want** to create a script to extract, transform, and load all 1,500 user records, **so that** all historical users and their relationships are preserved in the new system.

**Acceptance Criteria:**
1. A script is created that can connect to the legacy database and extract all user records.
2. The script successfully creates corresponding user records in the new platform via the Clerk/Django integration.
3. The script correctly preserves and re-establishes the user's role and their position in the managerial hierarchy.

### Story 12.3: Develop Transaction Migration Script
**As a** data engineer, **I want** to create a high-performance, idempotent script to migrate all 200,000 historical transaction records, **so that** all financial history is accurately moved to the new platform.

**Acceptance Criteria:**
1. A script is created to extract all historical transaction data from the legacy system.
2. The script correctly transforms the legacy data into the new `Claim` and `Payout` models.
3. The script is idempotent, meaning it can be run multiple times without creating duplicate records.
4. The script is optimized to process the full dataset within an acceptable time frame.

### Story 12.4: Implement Data Validation & Reconciliation Script
**As a** compliance officer, **I want** a script that validates the migrated data against the source data, **so that** I can be 100% confident in the accuracy and completeness of the migration.

**Acceptance Criteria:**
1. A validation script is created that can query both the legacy and new databases.
2. The script compares total record counts for users, customers, and transactions, and reports any discrepancies.
3. The script compares the sum of key financial fields (e.g., total payout amount) between the two systems to ensure financial integrity.
4. The script generates a reconciliation report summarizing the results.

### Story 12.5: Execute Staging Environment Migration & Validation
**As a** project manager, **I want** to perform a full dry run of the migration in a staging environment, **so that** we can identify and fix any issues before the production cutover.

**Acceptance Criteria:**
1. A clean staging environment that mirrors the production setup is prepared.
2. The user and transaction migration scripts are executed against the staging database.
3. The data validation and reconciliation script is run against the staging and legacy databases.
4. The reconciliation report shows 100% data integrity with no discrepancies.
5. Stakeholders review and formally sign off on the success of the staging migration.

### Story 12.6: Plan & Document Production Cutover
**As a** project manager, **I want** a detailed, step-by-step cutover plan for the production data migration, **so that** the final migration is executed smoothly with minimal risk and downtime.

**Acceptance Criteria:**
1. A formal cutover plan document is created.
2. The plan includes a detailed timeline, a checklist of all steps, and assigned responsibilities.
3. The plan clearly defines any required system downtime.
4. A comprehensive rollback plan is documented in case of critical failure.
5. The plan is reviewed and approved by all key stakeholders.

## Epic 13: Performance & Caching

**Epic Goal:** To implement and validate a comprehensive performance optimization strategy to ensure the platform meets the sub-2-second response time target at the 95th percentile. This includes setting up a multi-layer caching system, configuring a Content Delivery Network (CDN), and conducting load testing to verify scalability.

### Story 13.1: Implement Backend Caching with Redis
**As a** DevOps engineer, **I want** to integrate Redis as a caching layer for the backend, **so that** we can reduce database load and decrease response times for frequently accessed data.

**Acceptance Criteria:**
1. An AWS ElastiCache (Redis) instance is provisioned via Terraform.
2. The Django application is configured to use this Redis instance as its primary cache backend.
3. Caching is implemented for at least two high-traffic, read-heavy API endpoints (e.g., fetching campaign rules, user permissions).
4. A clear cache invalidation strategy is implemented to ensure data consistency.

### Story 13.2: Configure CDN for Frontend Assets
**As a** user, **I want** the application's frontend assets to load quickly regardless of my location, **so that** the initial page load and subsequent navigation feel instantaneous.

**Acceptance Criteria:**
1. An AWS CloudFront distribution is configured as a Content Delivery Network (CDN) for the frontend application's static assets (JavaScript, CSS, images).
2. The frontend build process is updated to serve all static assets via the CloudFront URL.
3. Appropriate cache-control headers are configured to optimize browser and CDN caching.

### Story 13.3: Performance Monitoring & Baseline Measurement
**As a** developer, **I want** to implement performance monitoring across the stack, **so that** we can establish a performance baseline and objectively measure the impact of our optimization efforts.

**Acceptance Criteria:**
1. A performance monitoring tool (e.g., Sentry, AWS CloudWatch) is configured to track API endpoint response times and frontend performance metrics (e.g., Core Web Vitals).
2. A dashboard is created to visualize the P95 response times for all critical API endpoints.
3. A baseline performance measurement is recorded before any major optimization work begins.

### Story 13.4: Optimize Slow Database Queries and API Endpoints
**As a** developer, **I want** to identify and optimize the slowest API endpoints and database queries, **so that** the overall application performance meets its target.

**Acceptance Criteria:**
1. Using the performance monitoring dashboard, the top 3-5 slowest-performing API endpoints are identified.
2. The underlying database queries for these endpoints are analyzed for inefficiencies.
3. Optimizations are applied (e.g., adding database indexes, refactoring code, applying caching).
4. The monitoring dashboard confirms a significant improvement in the P95 response time for the optimized endpoints.

### Story 13.5: Frontend Performance Optimization
**As a** developer, **I want** to implement frontend-specific performance optimizations, **so that** the user's browser can render and display pages as quickly as possible.

**Acceptance Criteria:**
1. The frontend build is configured for route-based code splitting, so users only download the code they need for the current page.
2. Images are optimized and lazy-loaded to avoid blocking initial page render.
3. The total JavaScript bundle size for the initial page load is minimized.

### Story 13.6: Execute Load Testing
**As a** DevOps engineer, **I want** to conduct a load test against a staging environment, **so that** I can verify the platform can handle the expected 1,000+ concurrent users without performance degradation.

**Acceptance Criteria:**
1. A load testing script is created (e.g., using k6 or JMeter).
2. The test simulates a realistic workload of 1,000 concurrent users performing common actions (logging in, viewing dashboards, submitting claims).
3. The test is run against a production-like staging environment.
4. Throughout the test, the P95 response time for all key endpoints remains below the 2-second target, and the error rate is below 0.1%.

## Epic 14: Compliance Hardening (SOC 2 & GDPR)

**Epic Goal:** To implement the specific technical controls, user-facing features, and auditable logging necessary to meet SOC 2 Type II and GDPR compliance requirements. This epic ensures the platform is secure, trustworthy, and respects user privacy, making it ready for enterprise customers.

### Story 14.1: Enhance Audit Trail for Compliance
**As a** compliance officer, **I want** the audit trail to log all significant system events, including data access and permission changes, **so that** we can provide complete evidence for SOC 2 audits.

**Acceptance Criteria:**
1. The audit trail system (from Epic 2) is enhanced to log events for any creation, modification, or deletion of key financial records (e.g., Claims, Payouts).
2. Any changes made to a user's role or permissions are explicitly logged in the audit trail.
3. Access to sensitive data (like viewing a W9) is logged as an audit event.

### Story 14.2: Verify Data Encryption at Rest
**As a** security engineer, **I want** to ensure all sensitive user and financial data is encrypted at rest, **so that** we can protect our customers' data and meet compliance standards.

**Acceptance Criteria:**
1. Encryption at rest is explicitly enabled and verified for the production RDS database instance.
2. Server-side encryption is enabled and verified for the S3 bucket that stores sensitive documents like W9s and invoices.
3. A documentation artifact is produced confirming the encryption settings.

### Story 14.3: Implement User Consent Management (GDPR)
**As a** new user, **I want** to be able to give explicit consent to the platform's terms of service and privacy policy, **so that** I have control over how my data is used.

**Acceptance Criteria:**
1. During the sign-up process, the user is presented with links to the Terms of Service and Privacy Policy and must check a box to consent.
2. The system records the fact that consent was given, along with the specific version of the documents the user consented to and a timestamp.
3. A user cannot complete registration without giving consent.

### Story 14.4: Implement "Right to Erasure" (GDPR)
**As an** administrator, **I want** a secure process to handle a user's request to be forgotten, **so that** we can comply with GDPR's right to erasure.

**Acceptance Criteria:**
1. An administrative function is created to handle user deletion requests.
2. The function anonymizes the user's PII (e.g., name, email, address) across all database records.
3. The function does *not* delete the financial transaction records themselves, preserving the integrity of financial reporting while removing the link to the individual.
4. The execution of this function is a highly privileged action and is logged in the audit trail.

### Story 14.5: Implement Data Portability (GDPR)
**As a** user, **I want** to be able to request an export of all my personal data, **so that** I can exercise my right to data portability under GDPR.

**Acceptance Criteria:**
1. A "Request My Data" button is available within the user's profile page.
2. Clicking the button creates a support ticket or an automated request for an administrator.
3. An administrative script is created that can be run to generate a machine-readable (JSON) file containing all of the requesting user's personal data.

### Story 14.6: Implement Security Monitoring for SOC 2
**As a** security engineer, **I want** to implement automated monitoring and alerting for security-related events, **so that** we can detect and respond to potential threats in real-time as required by SOC 2.

**Acceptance Criteria:**
1. Cloud security monitoring services (e.g., AWS GuardDuty, CloudTrail) are enabled and configured for the production environment.
2. Alerts are configured for critical security events, such as multiple failed login attempts from a single IP, or unauthorized API access attempts.
3. These alerts are automatically sent to a designated security contact or channel.

## Epic 15: Accessibility Compliance (WCAG)

**Epic Goal:** To perform a comprehensive audit and implement all necessary remediations across the entire frontend application to ensure full compliance with the Web Content Accessibility Guidelines (WCAG) 2.1 at the AA level. This makes the platform usable for people with disabilities and fulfills a key requirement for enterprise customers.

### Story 15.1: Setup Automated Accessibility Testing
**As a** developer, **I want** to integrate automated accessibility checking into our testing pipeline, **so that** we can catch common accessibility violations early and prevent regressions.

**Acceptance Criteria:**
1. An accessibility testing library (e.g., `axe-core`) is integrated into the Cypress E2E test suite.
2. The CI pipeline is configured to run these automated checks on every pull request.
3. The pipeline will fail if any new code introduces severe or critical accessibility violations.

### Story 15.2: Keyboard Navigation & Focus Management Audit
**As a** user who relies on a keyboard, **I want** to be able to navigate and operate the entire application without using a mouse, **so that** I can access all functionality.

**Acceptance Criteria:**
1. Every interactive element (links, buttons, form fields, menus) is reachable and operable using the Tab key.
2. The keyboard focus order is logical and follows the visual layout of the page.
3. A highly visible focus indicator is present on any element that currently has keyboard focus.
4. There are no "keyboard traps" where a user can navigate into a component but cannot navigate out.

### Story 15.3: Screen Reader Compatibility Audit
**As a** user who is blind, **I want** the application to be fully compatible with my screen reader, **so that** I can understand and interact with the content effectively.

**Acceptance Criteria:**
1. All interactive elements have a clear, accessible name, role, and state that can be announced by a screen reader (e.g., JAWS, NVDA).
2. All images and non-text content have appropriate alternative text.
3. The application uses semantic HTML and correct heading structure to define the page layout and importance of content.
4. Dynamic content changes and notifications are announced to screen reader users.

### Story 15.4: Color Contrast & Visuals Audit
**As a** user with low vision, **I want** all text to have sufficient color contrast, **so that** I can easily read the content on the screen.

**Acceptance Criteria:**
1. All text across the application meets the WCAG 2.1 AA contrast ratio of at least 4.5:1 for normal text and 3:1 for large text.
2. Information is not conveyed using color alone (e.g., a red border for an error must also include an icon and text).
3. The application is tested using a color blindness simulator to ensure usability.

### Story 15.5: Forms & Interactive Controls Audit
**As a** user with a disability, **I want** all forms and custom controls to be fully accessible, **so that** I can input data and interact with the application without barriers.

**Acceptance Criteria:**
1. Every form input is explicitly linked to a visible label.
2. All form validation errors are programmatically associated with their respective fields and are clearly announced to screen reader users.
3. Any custom-built components (e.g., date pickers, custom dropdowns) are fully keyboard-accessible and follow ARIA design patterns.

### Story 15.6: Generate Final Accessibility Conformance Report
**As a** compliance officer, **I want** a formal report documenting the platform's accessibility compliance, **so that** we can provide it to enterprise customers and stakeholders.

**Acceptance Criteria:**
1. A formal Accessibility Conformance Report (ACR), based on the standard Voluntary Product Accessibility Template (VPAT), is created.
2. The report documents the platform's level of conformance for each WCAG 2.1 AA success criterion.
3. The report is published and linked from the platform's website or portal.