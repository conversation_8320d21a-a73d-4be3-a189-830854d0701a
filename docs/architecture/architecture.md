# XD Incentives Platform Fullstack Architecture Document

## Introduction

This document outlines the complete fullstack architecture for the XD Incentives Platform, including backend systems, frontend implementation, and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack. This unified approach combines what would traditionally be separate backend and frontend architecture documents, streamlining the development process.

### Starter Template or Existing Project

This is a **greenfield project**. We will not be using a pre-packaged starter template. Instead, we will build the project from the ground up using standard, modern command-line tools (`Vite` for the React frontend, `django-admin` for the backend) as outlined in the PRD's foundational stories (Epic 1).

* **Rationale:** This approach provides maximum flexibility, avoids vendor lock-in from a specific template, and ensures our architecture is tailored precisely to our requirements without unnecessary boilerplate code.

### High-Level Architecture

#### Technical Summary

The XD Incentives Platform is a cloud-native, single-tenant SaaS application architected for security, scalability, and cost-efficiency on **AWS**. The architecture uses **AWS ECS on Fargate** for serverless container orchestration of the **Django REST API** backend, while the **React** frontend (built with **TypeScript**, **Vite**, and **Tanstack Start**) is served globally via **AWS CloudFront** from an S3 bucket. Component development and design system documentation are maintained in **Storybook**, ensuring consistent UI/UX across the platform. The entire system is deployed within a secure AWS VPC with distinct public and private subnets, and all infrastructure is managed via **Terraform**. The data layer leverages a high-availability **MySQL RDS** cluster with read replicas and a **Redis** cache, ensuring both performance and resilience.

#### High-Level Architecture Diagram

This diagram reflects the modernized AWS architecture, showing the flow from the user through various security and application layers to the data tier.

```mermaid
graph TD
    subgraph "User"
        U[Users]
    end

    subgraph "AWS Perimeter"
        R53[Route 53 DNS]
        WAF[WAF & Shield]
        CF[CloudFront CDN]
    end

    subgraph "AWS VPC"
        subgraph "Public Subnet"
            ALB[Application Load Balancer w/ SSL Termination]
        end
        subgraph "Private App Subnet"
            ECS[ECS Fargate Cluster]
            T1[Django Task 1]
            T2[Django Task 2]
            T3[...]
        end
        subgraph "Private Data Subnet"
            RDS_P[MySQL Primary]
            RDS_S[MySQL Standby]
            Redis[Redis Cache]
        end
        subgraph "Storage & Services (VPC Endpoints)"
            Secrets[Secrets Manager]
            SES[Email Service]
            S3_Static[S3 Static Files]
            S3_Media[S3 Media Files]
        end
        subgraph "Monitoring & Management"
            CW[CloudWatch Logs & Metrics]
        end
    end

    U --> R53 --> WAF --> CF --> ALB
    ALB --> ECS
    ECS --> T1 & T2 & T3
    
    T1 & T2 & T3 -- DB Credentials --> Secrets
    T1 & T2 & T3 -- Read/Write --> RDS_P
    RDS_P <--> RDS_S
    T1 & T2 & T3 -- Cache --> Redis
    T1 & T2 & T3 -- Send Email --> SES
    T1 & T2 & T3 -- Media Files --> S3_Media
    T1 & T2 & T3 -- Logs/Metrics --> CW
    CF -- Origin --> S3_Static
```

-----

### Security Architecture

Security is implemented in layers, from the public-facing perimeter down to the data and access layers, ensuring a defense-in-depth posture.

* **Perimeter Security:** All internet traffic is routed through **AWS WAF** and **CloudFront**, providing protection against common web exploits and DDoS attacks.
* **Network Security:** The application resides within a VPC with **public and private subnets**. The Application Load Balancer is in the public subnet, while the application and database servers are in private subnets, inaccessible from the internet. Access between layers is strictly controlled by **ECS and Database Security Groups**.
* **Application Security:** The ALB handles **SSL termination**, ensuring all traffic within the VPC is encrypted. The Django backend includes a **Data Validation Engine** to sanitize inputs.
* **Identity & Access:** Application containers run with specific **IAM Roles** that grant least-privilege access to other AWS services. Database credentials and other secrets are securely stored and rotated using **AWS Secrets Manager**.
* **Monitoring & Compliance:** All actions and logs are captured in **CloudWatch**, providing a comprehensive audit trail for compliance and security analysis. Databases and file storage are **encrypted at rest**.

-----

### Data Flow

The flow of data is designed to be unidirectional and processed through distinct stages, from input to storage and output.

* **Data Input:** Data enters the system from three primary sources: **Users** submitting sales data, **Admins** providing approvals, and **Bulk Imports** via CSV/Excel for batch data.
* **Application Processing:** All inputs are handled by the **Django REST API**. Incoming data is passed through a **Data Validation Engine** before being processed by the core **Business Logic Processor**. This processor orchestrates interactions with the data storage layer.
* **Data Storage:** The system utilizes three types of storage: a **Transactional DB (MySQL)** for core records, a **Cache Layer (Redis)** for performance, and **File Storage (S3)** for media and static files.
* **Data Output:** Processed data is exposed through several channels: **Reports & Dashboards** for users, **Email Notifications (SES)** for alerts, and **API Responses (JSON)** for the frontend application.

-----

### Deployment Architecture

Our deployment is fully automated using a CI/CD pipeline that ensures code is tested, scanned, and deployed consistently across environments.

For operational procedures and incident response, see [Operational Runbook](./operational-runbook.md). For disaster recovery procedures, see [Disaster Recovery Plan](./disaster-recovery-plan.md).

* **Source Control & CI/CD:** The process begins with a commit to the **GitHub** repository, which triggers a **GitHub Actions** pipeline.
* **Build & Scan:** The pipeline builds the Django application into a **Docker image**, which then undergoes automated **Security Scanning** to check for vulnerabilities.
* **Registry & Infrastructure:** The validated image is pushed to the **Amazon ECR** container registry. Simultaneously, **Terraform templates** are used to provision or update the necessary infrastructure for the target environment.
* **Environments:** The pipeline deploys sequentially to **Development**, **Staging**, and **Production** environments, each with its own isolated database and ECS cluster, ensuring a safe promotion path for new code.

-----

### Cost Optimization Strategy

The architecture incorporates several key strategies to manage operational costs without sacrificing performance or scalability.

* **Compute Optimization:** The ECS cluster will leverage **Fargate Spot** instances for potentially significant cost savings on stateless application tasks. It will also use **Auto Scaling** based on metrics to match capacity precisely to demand.
* **Database Optimization:** We will use **RDS Reserved Instances** for predictable production workloads to lower costs compared to on-demand pricing. **Read Replicas** will be used for handling intensive reporting queries, reducing the load on the primary database.
* **Storage Optimization:** S3 buckets will use **Intelligent-Tiering** to automatically move data to the most cost-effective storage class. **S3 Lifecycle Policies** will be used to transition or expire older data, such as logs.
* **Network Optimization:** Using **CloudFront** for caching reduces data transfer costs. We will also implement **VPC Endpoints** to allow services within our VPC to communicate with other AWS services without incurring costly NAT Gateway data processing fees.

-----

### Tech Stack

This table consolidates all technology decisions and serves as the single source of truth for all development.

| Category | Technology | Version | Purpose | Rationale |
| :--- | :--- | :--- | :--- | :--- |
| **Frontend Language** | TypeScript | 5.3+ | Adds static typing to JavaScript | Improves code quality and maintainability. |
| **Frontend Framework**| React | 19+ | Core UI library for building components | Modern, performant, and supported by a vast ecosystem. |
| **CSS Framework** | Tailwind CSS | 4+ | Utility-first CSS framework | Allows for rapid and consistent UI styling. |
| **Backend Language** | Python | 3.12.3+ | Primary backend programming language | Robust, mature, and excellent for data-heavy applications. |
| **Backend Framework**| Django | 5.2.3+ | High-level Python web framework | Enables rapid development of secure backend services. |
| **Database** | AWS RDS for MySQL | 8.4 | Primary relational database | Managed, scalable, and reliable SQL database for transactional data. |
| **Cache** | AWS ElastiCache (Redis) | 7.0+ | In-memory data store for caching | Improves application performance by caching frequently accessed data. |
| **File Storage** | AWS S3 | N/A | Object storage for user uploads | Secure, durable, and highly scalable storage for files. |
| **Compute** | AWS ECS on Fargate | N/A | Container orchestration service | Manages and scales our containerized backend services without needing to manage servers. |
| **Alternate Compute** | AWS EC2 | N/A | Virtual servers in the cloud | Provides foundational compute capacity for various tasks. |
| **Networking** | AWS Route 53 | N/A | Domain Name System (DNS) service | Manages the application's domain name and routes user traffic. |
| **Load Balancing** | AWS ALB | N/A | Application Load Balancer | Distributes incoming API traffic across multiple backend instances for scalability and reliability. |
| **CDN** | AWS CloudFront | N/A | Content Delivery Network | Caches and serves frontend assets globally for faster load times. |
| **Email Service** | AWS SES | N/A | Simple Email Service | Handles all outgoing transactional emails. |
| **Message Queue** | AWS SQS | N/A | Simple Queue Service | Decouples services and handles asynchronous tasks like report generation or email sending. |
| **Source Control** | GitHub | N/A | Git repository hosting | Manages the project's source code, pull requests, and version history. |
| **CI/CD** | GitHub Actions | N/A | Automation of build, test, and deploy | Integrates seamlessly with our source code for CI/CD workflows. |
| **IaC Tool** | Terraform | latest | Infrastructure as Code | Allows for automated and reproducible provisioning of all AWS infrastructure. |
| **Containerization** | Docker | latest | OS-level virtualization | Creates consistent development and production environments for all services. |
| **Authentication** | Clerk | latest | User management service | Handles complex auth needs like SSO, MFA, and session management securely. |
| **External API** | UPS | latest | Address validation service | Ensures data quality for customer records. |
| **Security** | Google reCAPTCHA | v3 | Bot protection service | Protects public forms like sign-up and login from automated abuse. |
| **Monitoring** | AWS CloudWatch | N/A | Infrastructure and application monitoring | Provides essential visibility into system health and performance. |

-----

### Data Models

This data model is based on a **Closure Table** pattern for hierarchies and a hybrid **RBAC/ABAC** system for permissions, providing maximum flexibility as requested.

#### How REBAC is Supported

This architecture was specifically designed to support REBAC (Relationship-Based Access Control) by modeling the relationships between users within the organizational structure. The **`organizational_hierarchy`** (Closure Table) explicitly stores every parent-child relationship, allowing us to efficiently query if one user is a manager of another and grant access based on that relationship.

#### Data Model Interfaces

**Organizational Units & Hierarchy**

* **Purpose:** These tables define the client's specific organizational structure (e.g., regions, territories, teams) and represent the relationships between them in a highly efficient manner.

<!-- end list -->

```typescript
export interface OrganizationalUnit {
  id: string;
  name: string;
  nodeType: 'national' | 'regional' | 'distributor' | 'team'; // Example types
  level: number;
  metadata?: Record<string, any>;
}
```

**Roles & Permissions**

* **Purpose:** These tables define granular permissions, group them into flexible roles, and allow for conditional logic (ABAC).

<!-- end list -->

```typescript
export interface Permission {
  id: string;
  name: string; // e.g., 'approve_sales'
  resource: string; // e.g., 'claims'
  action: string; // e.g., 'approve'
}

export interface Role {
  id: string;
  name: string;
  isInheritable: boolean;
  hierarchyLevel: number;
}
```

**User & Assignments**

* **Purpose:** These tables define the users and link them to specific roles within a specific part of the organizational hierarchy.

<!-- end list -->

```typescript
export interface UserRoleAssignment {
  id: string;
  userId: string;
  roleId: string;
  orgUnitId: string;
  isActive: boolean;
  expiresAt?: string; // ISO 8601 Date String
}
```

**Application-Specific Models**

* **Purpose:** The core business entities of the application.

<!-- end list -->

```typescript
export interface Address {
  street1: string;
  street2?: string;
  city: string;
  state: string;
  zipCode: string;
}

export interface Customer {
  id: string;
  name: string;
  address: Address;
  status: 'Pending' | 'Approved' | 'Denied';
  tenureInYears: number;
  ownerId: string; // ID of the MSR who owns the customer
}

export interface Claim {
  id: string;
  status: 'Submitted' | 'Approved' | 'Denied' | 'Processed';
  invoiceUrl: string;
  submittedAt: string; // ISO 8601 Date String
  userId: string; // The MSR who submitted
  customerId: string;
}

export interface Campaign {
  id: string;
  name: string;
  description: string;
  startDate: string; // ISO 8601 Date String
  endDate: string; // ISO 8601 Date String
}

export interface Payout {
  id: string;
  amount: number;
  status: 'Pending Payment' | 'Paid' | 'Disputed';
  processedAt: string; // ISO 8601 Date String
  userId: string;
  claimId: string;
  campaignId: string;
}
```

-----

### API Specification

We will use the **OpenAPI 3.0 standard** to define a RESTful API. This specification serves as the formal contract between our frontend and backend.

```yaml
openapi: 3.0.0
info:
  title: "XD Incentives Platform API"
  version: "1.0.0"
  description: "API for managing channel partner incentives, claims, and payouts."
servers:
  - url: "/api/v1"
    description: "API Version 1"
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
security:
  - bearerAuth: []
paths:
  /health:
    get:
      tags: [Health]
      summary: "Check API Health"
      security: []
      responses:
        '200':
          description: "Service is healthy"
  /customers:
    post:
      tags: [Customers]
      summary: "Submit a new Customer"
      responses:
        '201':
          description: "Customer created successfully"
  /claims/queue:
    get:
      tags: [Claims]
      summary: "Get pending claim review queue"
      responses:
        '200':
          description: "A list of pending claims"
```

-----

### Frontend Architecture

#### React Application Structure

The frontend is built as a modern React application using **TypeScript** for type safety, **Vite** for fast development builds, and **Tanstack Start** for full-stack React framework capabilities. The architecture follows component-driven development principles with **Storybook** serving as the primary tool for component development, documentation, and design system management.

```plaintext
frontend/
├── src/
│   ├── components/           # React components
│   │   ├── ui/              # Base UI components (Shadcn)
│   │   ├── layout/          # Layout components
│   │   ├── forms/           # Form-specific components
│   │   └── data/            # Data display components
│   ├── pages/               # Page components
│   ├── hooks/               # Custom React hooks
│   ├── services/            # API integration layer
│   ├── stores/              # Zustand stores
│   ├── utils/               # Utility functions
│   ├── types/               # TypeScript definitions
│   └── assets/              # Static assets
├── .storybook/              # Storybook configuration
├── stories/                 # Component stories
├── public/                  # Public static files
├── tests/                   # Test files
└── dist/                    # Build output
```

#### Technology Stack

| Category | Technology | Purpose |
|----------|------------|---------|
| **Framework** | React 18+ | UI component framework |
| **Language** | TypeScript 5+ | Type-safe JavaScript |
| **Meta-Framework** | Tanstack Start | Full-stack React framework |
| **Build Tool** | Vite 6+ | Fast build tool & dev server |
| **Styling** | Tailwind CSS 3+ | Utility-first CSS framework |
| **UI Components** | Shadcn UI | Accessible component library |
| **State Management** | Tanstack Query + Zustand | Server & client state |
| **Routing** | Tanstack Router | Type-safe routing |
| **Component Dev** | Storybook 9+ | Component documentation |
| **Testing** | Vitest | Unit & integration testing |
| **Linting** | Biome | Fast linting & formatting |

#### Component Architecture

The frontend follows a hierarchical component structure based on **Atomic Design** principles:

**Base Components (`src/components/ui/`)**
- Built with Shadcn UI patterns
- Fully accessible (WCAG 2.1 AA compliant)
- TypeScript interfaces for all props
- Consistent design tokens via Tailwind CSS
- Examples: Button, Input, Card, Modal, Loading

**Composite Components (`src/components/`)**
- Layout components (Header, Sidebar, Footer)
- Form components (LoginForm, ClaimSubmissionForm)
- Data components (UserTable, ClaimsList)
- Feature components (Dashboard, Reports)

**Page Components (`src/pages/`)**
- Route-level components
- Data fetching orchestration
- Layout composition
- Examples: HomePage, DashboardPage, ClaimsPage

#### State Management Strategy

**Server State (Tanstack Query)**
- API data fetching and caching
- Optimistic updates
- Background refetching
- Error handling and retry logic
- Cache invalidation strategies

**Client State (Zustand)**
- Authentication state
- UI state (theme, modals, notifications)
- Form state for complex multi-step processes
- User preferences and settings

**URL State (Tanstack Router)**
- Navigation state
- Search parameters
- Route parameters
- Type-safe route definitions

#### API Integration Layer

**Centralized API Client (`src/services/api-client.ts`)**
```typescript
// Base API client with interceptors
const apiClient = {
  get: <T>(url: string) => fetch(url).then(r => r.json() as T),
  post: <T>(url: string, data: any) => fetch(url, {
    method: 'POST',
    body: JSON.stringify(data)
  }).then(r => r.json() as T),
  // ... other methods
}
```

**Custom Hooks for API Operations (`src/hooks/`)**
```typescript
// Type-safe API hooks using Tanstack Query
export const useMembers = () => {
  return useQuery({
    queryKey: ['members'],
    queryFn: () => apiClient.get<Member[]>('/api/members/'),
  })
}

export const useCreateClaim = () => {
  return useMutation({
    mutationFn: (data: CreateClaimRequest) => 
      apiClient.post<Claim>('/api/claims/', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['claims'] })
    }
  })
}
```

#### Design System and Component Development

**Storybook Integration**
- Component documentation and playground
- Design token visualization
- Accessibility testing addon
- Visual regression testing
- Cross-browser testing capabilities

**Design System Structure**
```plaintext
Design System/
├── Tokens/
│   ├── Colors (Citgo brand palette)
│   ├── Typography (Univers font family)
│   ├── Spacing (4px base unit)
│   └── Shadows & Effects
├── Components/
│   ├── Primitives (Button, Input, etc.)
│   ├── Patterns (Card, Modal, etc.)
│   └── Templates (Page layouts)
└── Guidelines/
    ├── Accessibility (WCAG 2.1 AA)
    ├── Responsive Design
    └── Animation Principles
```

#### Development Workflow

**Component Development Process**
1. **Design in Storybook**: Create component stories first
2. **Type Definitions**: Define TypeScript interfaces
3. **Implementation**: Build component with accessibility
4. **Testing**: Unit tests with Vitest
5. **Documentation**: Update stories and docs
6. **Integration**: Use in pages and features

**Quality Assurance**
- **TypeScript**: Strict type checking enabled
- **Biome**: Fast linting and formatting
- **Vitest**: Unit and integration testing
- **Storybook**: Visual testing and documentation
- **A11y Testing**: Automated accessibility checks

#### Performance Optimization

**Build Optimizations**
- Code splitting at route level
- Tree shaking for unused code
- Asset optimization (images, fonts)
- Bundle analysis and monitoring
- Lazy loading for non-critical components

**Runtime Optimizations**
- React Suspense for async components
- Virtualization for large lists
- Image optimization with next-gen formats
- Service worker for caching strategies
- Performance monitoring with Core Web Vitals

#### Accessibility Implementation

**WCAG 2.1 AA Compliance**
- Semantic HTML structure
- Proper heading hierarchy
- Keyboard navigation support
- Screen reader compatibility
- High contrast ratios (4.5:1 minimum)
- Focus management and indicators

**Accessibility Testing**
- Automated testing in Storybook
- Manual testing with screen readers
- Keyboard-only navigation testing
- Color contrast validation
- Performance impact on assistive technologies

#### Security Considerations

**Frontend Security Measures**
- Content Security Policy (CSP) headers
- XSS prevention through React's built-in protections
- Secure cookie handling for authentication
- Input validation and sanitization
- Dependency vulnerability scanning

**API Security Integration**
- JWT token management
- Automatic token refresh
- Secure storage of sensitive data
- Request signing for critical operations
- Rate limiting compliance

#### Deployment and Distribution

**Production Build Process**
- Static asset generation
- CDN distribution via CloudFront
- Cache invalidation strategies
- Progressive Web App features
- Service worker implementation

**Environment Management**
- Environment-specific configurations
- Feature flag integration
- A/B testing infrastructure
- Performance monitoring setup
- Error tracking and reporting

-----

### Unified Project Structure

The project will be organized as a monorepo with top-level directories for the frontend, backend, infrastructure-as-code, documentation, and operational scripts.

```plaintext
/
├── .env.example
├── .github/
│   └── workflows/
├── backend/
│   ├── apps/
│   └── config/
├── docs/
├── docker/
├── frontend/
│   └── src/
├── scripts/
├── terraform/
│   ├── environments/
│   └── modules/
├── .gitignore
├── README.md
└── docker-compose.yml
```

-----

### Development Workflow

Our local development environment is fully containerized with Docker to ensure consistency.

#### Prerequisites

* Git, Docker & Docker Compose, Node.js (LTS), Python (3.12.3+), Terraform CLI

#### Initial Setup

```bash
./scripts/setup.sh
cp .env.example .env
# Populate .env with credentials
```

#### Development Commands

```bash
docker-compose up
docker-compose down
```

-----

### Testing Strategy

Our strategy is based on the "Testing Pyramid" with a strong foundation of unit tests, supported by integration and end-to-end tests.

```plaintext
      /|\
     / | \   <-- End-to-End Tests (Cypress)
    /___|___\
   /         \  <-- Integration Tests (Pytest/RTL)
  /_____________\
 /                 \ <-- Unit Tests (Pytest/Jest)
/___________________\
```

-----

### Coding Standards

A concise set of critical, non-negotiable rules. Most formatting is handled automatically by **Biome** (frontend) and **Black** (backend).

#### Critical Fullstack Rules

1. **Enforce API Contract:** Frontend data types must strictly adhere to the OpenAPI Specification.
2. **Use the Data Fetching Layer:** The frontend must use TanStack Query custom hooks for all API interactions.
3. **Centralize Data Access:** The backend must use the Repository Pattern for all database access.
4. **Isolate Environment Variables:** Never access environment variables directly in application logic.
5. **No Sensitive Information in Logs:** Never log PII, passwords, API keys, or tokens in plaintext.

#### Git Workflow & Commit Guidelines

We will follow the **"Integrity Git Workflow"** with `main` and `develop` branches. All commits must follow the **Seven Guidelines of a Great Commit Message**.

-----

### Error Handling Strategy

Our strategy is to handle errors gracefully, provide clear feedback to users, and enable rapid diagnosis for developers. All API error responses will use a standardized JSON structure. Sentry will be used for centralized error tracking.

For detailed resilience patterns including circuit breakers, retry policies, and timeout configurations, see [Resilience Patterns Documentation](./resilience-patterns.md).

-----

### Monitoring and Observability

Our strategy provides visibility into frontend user experience, backend infrastructure, and application performance.

#### Monitoring Stack

* **Frontend Monitoring (Sentry):** Tracks Core Web Vitals and JavaScript errors.
* **Backend Monitoring (AWS CloudWatch & Sentry):** CloudWatch for infrastructure metrics (ECS, RDS) and Sentry for application performance monitoring (APM).
* **Log Aggregation (AWS CloudWatch Logs):** Centralized, searchable repository for all logs.

For specific alerting thresholds, escalation procedures, and monitoring configurations, see [Monitoring and Alerting Configuration](./monitoring-alerting.md).

-----