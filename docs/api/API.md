# XD Incentives API Documentation

## Table of Contents
1. [Authentication](#authentication)
2. [Core Member APIs](#core-member-apis)
3. [Team Management APIs](#team-management-apis)
4. [Member Type APIs](#member-type-apis)
5. [Terms & Privacy APIs](#terms--privacy-apis)
6. [Communication APIs](#communication-apis)
7. [Password Reset APIs](#password-reset-apis)
8. [Member Profile Management APIs](#member-profile-management-apis)
9. [Member Hierarchy Management APIs](#member-hierarchy-management-apis)
10. [Error Handling](#error-handling)
11. [Rate Limiting](#rate-limiting)

---

## Authentication

All API endpoints require JWT authentication. Include the Bearer token in the Authorization header.

### Get Access Token
```bash
curl -X POST http://localhost:8000/api/token/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your_username",
    "password": "your_password"
  }'
```

**Response:**
```json
{
  "access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Refresh Token
```bash
curl -X POST http://localhost:8000/api/token/refresh/ \
  -H "Content-Type: application/json" \
  -d '{
    "refresh": "your_refresh_token"
  }'
```

---

## Core Member APIs

### Get Member Details
**Endpoint:** `GET /api/member/details/`

**Description:** Get current user's member details

**Headers:**
```
Authorization: Bearer <your_access_token>
```

**Response:**
```json
{
  "id": 1,
  "username": "john_doe",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "status": 1,
  "member_type": {
    "id": 2,
    "name": "Manager",
    "slug": "manager"
  },
  "region": {
    "id": 1,
    "title": "North America"
  },
  "permissions": ["member.view", "communication.send"],
  "features": ["reports", "analytics"],
  "navigation": [
    {
      "label": "Dashboard",
      "url": "/",
      "icon": "home",
      "order": 1
    }
  ]
}
```

### Get Members List (Admin Only)
**Endpoint:** `GET /api/members/`

**Description:** Get paginated list of all members with filtering

**Query Parameters:**
- `search` (string): Search by name, email, or username
- `member_type` (integer): Filter by member type ID
- `region` (integer): Filter by region ID
- `status` (integer): Filter by status
- `page` (integer): Page number (default: 1)
- `limit` (integer): Items per page (default: 20)

**Example:**
```bash
curl -X GET "http://localhost:8000/api/members/?search=john&member_type=2&page=1&limit=10" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "members": [
    {
      "id": 1,
      "username": "john_doe",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "status": 1,
      "member_type": {
        "id": 2,
        "name": "Manager",
        "slug": "manager"
      },
      "region": {
        "id": 1,
        "title": "North America"
      },
      "phone_cell": "+1234567890",
      "created": "2025-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total_count": 150,
    "total_pages": 15,
    "has_next": true,
    "has_previous": false,
    "next_page": 2,
    "previous_page": null
  },
  "filters": {
    "search": "john",
    "member_type": 2,
    "region": null,
    "status": null
  }
}
```

### Member Search (Admin Only)
**Endpoint:** `GET /api/members/search/`

**Description:** Advanced search with field-specific queries

**Query Parameters:**
- `q` (string): Search query
- `fields` (string): Comma-separated fields to search (name, email, phone, etc.)
- `limit` (integer): Maximum results (default: 10)

**Example:**
```bash
curl -X GET "http://localhost:8000/api/members/search/?q=john&fields=name,email&limit=5" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "results": [
    {
      "id": 1,
      "username": "john_doe",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "match_score": 0.95
    }
  ],
  "total_results": 3,
  "search_query": "john",
  "searched_fields": ["name", "email"]
}
```

### Organization Chart (Admin Only)
**Endpoint:** `GET /api/organization-chart/`

**Description:** Get hierarchical organization structure

**Query Parameters:**
- `depth` (integer): Maximum depth to traverse (default: 3)
- `include_inactive` (boolean): Include inactive members (default: false)
- `member_type` (integer): Filter by member type
- `region` (integer): Filter by region

**Example:**
```bash
curl -X GET "http://localhost:8000/api/organization-chart/?depth=3&include_inactive=false" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "hierarchy": [
    {
      "member": {
        "id": 1,
        "username": "ceo",
        "name": "John CEO",
        "member_type": "Executive"
      },
      "subordinates": [
        {
          "member": {
            "id": 2,
            "username": "manager1",
            "name": "Jane Manager",
            "member_type": "Manager"
          },
          "subordinates": []
        }
      ]
    }
  ],
  "statistics": {
    "total_members": 150,
    "max_depth": 4,
    "average_span": 3.2
  }
}
```

### Member Hierarchy
**Endpoint:** `GET /api/member/{id}/hierarchy/`

**Description:** Get hierarchy information for a specific member

**Example:**
```bash
curl -X GET "http://localhost:8000/api/member/1/hierarchy/" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "member": {
    "id": 1,
    "username": "john_doe",
    "name": "John Doe"
  },
  "managers": [
    {
      "id": 5,
      "username": "manager1",
      "name": "Jane Manager",
      "relationship_type": "direct_manager",
      "is_primary": true
    }
  ],
  "subordinates": [
    {
      "id": 10,
      "username": "subordinate1",
      "name": "Bob Subordinate",
      "relationship_type": "direct_manager"
    }
  ]
}
```

---

## Team Management APIs

### Get Teams List
**Endpoint:** `GET /api/teams/`

**Description:** Get all teams with member counts

**Query Parameters:**
- `team_type` (string): Filter by team type
- `is_active` (boolean): Filter by active status
- `page` (integer): Page number
- `limit` (integer): Items per page

**Example:**
```bash
curl -X GET "http://localhost:8000/api/teams/?team_type=sales&is_active=true" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "teams": [
    {
      "id": 1,
      "name": "Sales Team A",
      "team_type": "sales",
      "description": "Primary sales team",
      "team_lead": {
        "id": 5,
        "username": "sales_lead",
        "name": "Sales Lead"
      },
      "member_count": 8,
      "is_active": true,
      "created": "2025-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total_count": 25,
    "total_pages": 2
  }
}
```

### Team Hierarchy
**Endpoint:** `GET /api/teams/{id}/hierarchy/`

**Description:** Get hierarchical structure of a specific team

**Query Parameters:**
- `depth` (integer): Maximum depth to traverse
- `role` (string): Filter by member role

**Example:**
```bash
curl -X GET "http://localhost:8000/api/teams/1/hierarchy/?depth=2&role=manager" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "team": {
    "id": 1,
    "name": "Sales Team A",
    "team_type": "sales"
  },
  "hierarchy": [
    {
      "member": {
        "id": 5,
        "username": "team_lead",
        "name": "Team Lead",
        "role": "lead"
      },
      "subordinates": [
        {
          "member": {
            "id": 10,
            "username": "sales_rep1",
            "name": "Sales Rep 1",
            "role": "member"
          }
        }
      ]
    }
  ]
}
```

### Team Members
**Endpoint:** `GET /api/teams/{id}/members/`

**Description:** Get all members of a specific team

**Query Parameters:**
- `role` (string): Filter by member role
- `member_type` (integer): Filter by member type
- `active_only` (boolean): Show only active members (default: true)

**Example:**
```bash
curl -X GET "http://localhost:8000/api/teams/1/members/?role=manager&active_only=true" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "team": {
    "id": 1,
    "name": "Sales Team A"
  },
  "members": [
    {
      "id": 5,
      "username": "manager1",
      "name": "Jane Manager",
      "role": "manager",
      "is_primary": true,
      "start_date": "2025-01-01",
      "end_date": null
    }
  ],
  "statistics": {
    "total_members": 8,
    "by_role": {
      "manager": 2,
      "member": 6
    }
  }
}
```

### Member Teams
**Endpoint:** `GET /api/member/{id}/teams/`

**Description:** Get all teams a specific member belongs to

**Query Parameters:**
- `role` (string): Filter by member role
- `primary_only` (boolean): Show only primary teams
- `active_only` (boolean): Show only active memberships

**Example:**
```bash
curl -X GET "http://localhost:8000/api/member/1/teams/?role=admin&primary_only=true" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "member": {
    "id": 1,
    "username": "john_doe",
    "name": "John Doe"
  },
  "teams": [
    {
      "id": 1,
      "name": "Sales Team A",
      "team_type": "sales",
      "role": "admin",
      "is_primary": true,
      "start_date": "2025-01-01",
      "end_date": null
    }
  ],
  "statistics": {
    "total_teams": 3,
    "primary_teams": 1,
    "by_role": {
      "admin": 1,
      "member": 2
    }
  }
}
```

---

## Member Type APIs

### Get Member Types
**Endpoint:** `GET /api/member-types/`

**Description:** Get all member types with their configurations

**Example:**
```bash
curl -X GET "http://localhost:8000/api/member-types/" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "member_types": [
    {
      "id": 1,
      "name": "Admin",
      "slug": "admin",
      "description": "Administrative users",
      "permissions": {
        "permissions": ["admin.access", "member.manage"]
      },
      "page_access": {
        "pages": {
          "admin": {"access": true},
          "reports": {"access": true}
        }
      },
      "navigation": {
        "menu": [
          {
            "label": "Dashboard",
            "url": "/",
            "icon": "home",
            "order": 1
          }
        ]
      },
      "feature_flags": {
        "features": ["reports", "analytics", "export"]
      },
      "is_active": true,
      "can_signup": false,
      "requires_approval": true,
      "created": "2025-01-15T10:30:00Z"
    }
  ]
}
```

### Get Member Type Detail
**Endpoint:** `GET /api/member-types/{id}/`

**Description:** Get detailed information about a specific member type

**Example:**
```bash
curl -X GET "http://localhost:8000/api/member-types/1/" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "id": 1,
  "name": "Admin",
  "slug": "admin",
  "description": "Administrative users",
  "permissions": {
    "permissions": ["admin.access", "member.manage", "communication.send"]
  },
  "page_access": {
    "pages": {
      "admin": {"access": true, "permissions": ["admin.access"]},
      "reports": {"access": true, "permissions": ["reports.view"]}
    }
  },
  "navigation": {
    "menu": [
      {
        "label": "Dashboard",
        "url": "/",
        "icon": "home",
        "order": 1
      },
      {
        "label": "Member Management",
        "url": "/member/list/",
        "icon": "users",
        "order": 2,
        "children": [
          {"label": "All Members", "url": "/member/list/", "order": 1},
          {"label": "Create Member", "url": "/member/create/", "order": 2}
        ]
      }
    ]
  },
  "feature_flags": {
    "features": ["reports", "analytics", "export", "bulk_operations"]
  },
  "dashboard_layout": {
    "layout": [
      {"widget": "member_stats", "position": "top-left", "size": "medium"},
      {"widget": "recent_activity", "position": "top-right", "size": "medium"}
    ]
  },
  "theme_settings": {
    "primary_color": "#1f2937",
    "secondary_color": "#374151",
    "accent_color": "#3b82f6"
  },
  "is_active": true,
  "can_signup": false,
  "requires_approval": true,
  "auto_approve": false,
  "max_subordinates": 0,
  "can_manage_types": [2, 3],
  "created": "2025-01-15T10:30:00Z",
  "modified": "2025-01-15T10:30:00Z"
}
```

---

## Terms & Privacy APIs

### Get Current Terms
**Endpoint:** `GET /api/terms/`

**Description:** Get current terms and conditions

**Example:**
```bash
curl -X GET "http://localhost:8000/api/terms/" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "terms": {
    "id": 1,
    "title": "XD Incentives Terms and Conditions v1.0",
    "current": true,
    "body_html": "<h2>XD Incentives Terms and Conditions</h2><p>...</p>",
    "body_text": "XD Incentives Terms and Conditions\n\n1. Acceptance...",
    "created": "2025-01-01T00:00:00Z",
    "modified": "2025-01-01T00:00:00Z"
  },
  "user_acceptance": {
    "has_accepted": true,
    "accepted_at": "2025-01-15T10:30:00Z"
  }
}
```

### Accept Terms
**Endpoint:** `POST /api/terms/accept/`

**Description:** Accept the current terms and conditions

**Example:**
```bash
curl -X POST "http://localhost:8000/api/terms/accept/" \
  -H "Authorization: Bearer <your_access_token>" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully accepted terms and conditions: XD Incentives Terms and Conditions v1.0",
  "terms": {
    "id": 1,
    "title": "XD Incentives Terms and Conditions v1.0",
    "current": true
  },
  "acceptance": {
    "accepted_at": "2025-01-15T10:30:00Z",
    "was_new_acceptance": true,
    "previously_accepted": false
  }
}
```

### Get Terms History (Admin Only)
**Endpoint:** `GET /api/terms/history/`

**Description:** Get all terms versions with acceptance statistics

**Example:**
```bash
curl -X GET "http://localhost:8000/api/terms/history/" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "terms_versions": [
    {
      "id": 1,
      "title": "XD Incentives Terms and Conditions v1.0",
      "current": true,
      "body_html": "<h2>XD Incentives Terms and Conditions</h2>...",
      "body_text": "XD Incentives Terms and Conditions...",
      "created": "2025-01-01T00:00:00Z",
      "modified": "2025-01-01T00:00:00Z",
      "acceptance_count": 150
    }
  ],
  "total_versions": 3,
  "current_version": {
    "id": 1,
    "title": "XD Incentives Terms and Conditions v1.0",
    "current": true
  }
}
```

### Get Current Privacy Policy
**Endpoint:** `GET /api/privacy/`

**Description:** Get current privacy policy

**Example:**
```bash
curl -X GET "http://localhost:8000/api/privacy/" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "privacy": {
    "id": 1,
    "title": "XD Incentives Privacy Policy v1.0",
    "current": true,
    "body_html": "<h2>XD Incentives Privacy Policy</h2><p>...</p>",
    "body_text": "XD Incentives Privacy Policy\n\n1. Information Collection...",
    "created": "2025-01-01T00:00:00Z",
    "modified": "2025-01-01T00:00:00Z"
  },
  "user_acceptance": {
    "has_accepted": false,
    "accepted_at": null
  }
}
```

### Accept Privacy Policy
**Endpoint:** `POST /api/privacy/accept/`

**Description:** Accept the current privacy policy

**Example:**
```bash
curl -X POST "http://localhost:8000/api/privacy/accept/" \
  -H "Authorization: Bearer <your_access_token>" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully accepted privacy policy: XD Incentives Privacy Policy v1.0",
  "privacy": {
    "id": 1,
    "title": "XD Incentives Privacy Policy v1.0",
    "current": true
  },
  "acceptance": {
    "accepted_at": "2025-01-15T10:30:00Z",
    "was_new_acceptance": true,
    "previously_accepted": false
  }
}
```

### Get Privacy History (Admin Only)
**Endpoint:** `GET /api/privacy/history/`

**Description:** Get all privacy policy versions with acceptance statistics

**Example:**
```bash
curl -X GET "http://localhost:8000/api/privacy/history/" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "privacy_versions": [
    {
      "id": 1,
      "title": "XD Incentives Privacy Policy v1.0",
      "current": true,
      "body_html": "<h2>XD Incentives Privacy Policy</h2>...",
      "body_text": "XD Incentives Privacy Policy...",
      "created": "2025-01-01T00:00:00Z",
      "modified": "2025-01-01T00:00:00Z",
      "acceptance_count": 120
    }
  ],
  "total_versions": 2,
  "current_version": {
    "id": 1,
    "title": "XD Incentives Privacy Policy v1.0",
    "current": true
  }
}
```

---

## Communication APIs

### Get User Communications
**Endpoint:** `GET /api/communications/`

**Description:** Get communications for the authenticated user

**Query Parameters:**
- `type` (string): Filter by communication type (email, notification, message, sms, push)
- `status` (string): Filter by status (pending, sent, delivered, read, failed)
- `priority` (string): Filter by priority (low, normal, high, urgent)
- `category` (string): Filter by category
- `page` (integer): Page number (default: 1)
- `limit` (integer): Items per page (default: 20)

**Example:**
```bash
curl -X GET "http://localhost:8000/api/communications/?type=email&status=sent&page=1&limit=20" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "communications": [
    {
      "id": 1,
      "communication_type": "email",
      "status": "sent",
      "priority": "normal",
      "subject": "Welcome to XD Incentives",
      "title": "Welcome Email",
      "content": "Welcome to XD Incentives! We're excited to have you on board.",
      "content_html": "<h2>Welcome to XD Incentives!</h2><p>We're excited to have you on board.</p>",
      "category": "welcome",
      "tags": ["welcome", "onboarding"],
      "from_member": {
        "id": 5,
        "name": "System Admin",
        "email": "<EMAIL>"
      },
      "from_email": "<EMAIL>",
      "from_name": "XD Incentives",
      "sent_at": "2025-01-15T10:30:00Z",
      "delivered_at": "2025-01-15T10:31:00Z",
      "read_at": "2025-01-15T10:35:00Z",
      "failed_at": null,
      "failure_reason": null,
      "scheduled_for": null,
      "created": "2025-01-15T10:30:00Z",
      "modified": "2025-01-15T10:35:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total_count": 45,
    "total_pages": 3,
    "has_next": true,
    "has_previous": false,
    "next_page": 2,
    "previous_page": null
  },
  "filters": {
    "type": "email",
    "status": "sent",
    "priority": null,
    "category": null
  }
}
```

### Create Communication
**Endpoint:** `POST /api/communications/`

**Description:** Create a new communication (requires `communication.send` permission)

**Headers:**
```
Authorization: Bearer <your_access_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "communication_type": "email",
  "to_member_id": 2,
  "to_email": "<EMAIL>",
  "to_phone": "+1234567890",
  "from_email": "<EMAIL>",
  "from_name": "Sender Name",
  "subject": "Welcome Message",
  "title": "Welcome",
  "content": "Welcome to our platform!",
  "content_html": "<h2>Welcome</h2><p>Welcome to our platform!</p>",
  "template_name": "welcome_email",
  "category": "welcome",
  "tags": "welcome,onboarding",
  "priority": "normal",
  "scheduled_for": "2025-01-16T10:00:00Z"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Communication created successfully",
  "communication": {
    "id": 123,
    "communication_type": "email",
    "status": "pending",
    "priority": "normal",
    "subject": "Welcome Message",
    "title": "Welcome",
    "content": "Welcome to our platform!",
    "created": "2025-01-15T10:30:00Z"
  }
}
```

### Get Communication Detail
**Endpoint:** `GET /api/communications/{id}/`

**Description:** Get detailed information about a specific communication

**Example:**
```bash
curl -X GET "http://localhost:8000/api/communications/1/" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "id": 1,
  "communication_type": "email",
  "status": "read",
  "priority": "normal",
  "subject": "Welcome to XD Incentives",
  "title": "Welcome Email",
  "content": "Welcome to XD Incentives! We're excited to have you on board.",
  "content_html": "<h2>Welcome to XD Incentives!</h2><p>We're excited to have you on board.</p>",
  "category": "welcome",
  "tags": ["welcome", "onboarding"],
  "to_member": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "to_email": "<EMAIL>",
  "to_phone": null,
  "from_member": {
    "id": 5,
    "name": "System Admin",
    "email": "<EMAIL>"
  },
  "from_email": "<EMAIL>",
  "from_name": "XD Incentives",
  "template_name": "welcome_email",
  "sent_at": "2025-01-15T10:30:00Z",
  "delivered_at": "2025-01-15T10:31:00Z",
  "read_at": "2025-01-15T10:35:00Z",
  "failed_at": null,
  "failure_reason": null,
  "retry_count": 0,
  "max_retries": 3,
  "scheduled_for": null,
  "created": "2025-01-15T10:30:00Z",
  "modified": "2025-01-15T10:35:00Z"
}
```

### Update Communication
**Endpoint:** `PATCH /api/communications/{id}/`

**Description:** Update a communication (e.g., mark as read)

**Headers:**
```
Authorization: Bearer <your_access_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "status": "read",
  "read_at": "2025-01-15T10:35:00Z"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Communication updated successfully",
  "communication": {
    "id": 1,
    "status": "read",
    "read_at": "2025-01-15T10:35:00Z"
  }
}
```

### Communications Admin (Admin Only)
**Endpoint:** `GET /api/communications/admin/`

**Description:** Get all communications with statistics (requires `communication.view_all` permission)

**Query Parameters:**
- `type` (string): Filter by communication type
- `status` (string): Filter by status
- `priority` (string): Filter by priority
- `category` (string): Filter by category
- `to_member` (integer): Filter by recipient member ID
- `from_member` (integer): Filter by sender member ID
- `page` (integer): Page number (default: 1)
- `limit` (integer): Items per page (default: 20)

**Example:**
```bash
curl -X GET "http://localhost:8000/api/communications/admin/?type=email&status=sent&page=1&limit=20" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "communications": [
    {
      "id": 1,
      "communication_type": "email",
      "status": "sent",
      "priority": "normal",
      "subject": "Welcome to XD Incentives",
      "title": "Welcome Email",
      "content": "Welcome to XD Incentives! We're excited to have you on board...",
      "category": "welcome",
      "tags": ["welcome", "onboarding"],
      "to_member": {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "to_email": "<EMAIL>",
      "to_phone": null,
      "from_member": {
        "id": 5,
        "name": "System Admin",
        "email": "<EMAIL>"
      },
      "from_email": "<EMAIL>",
      "from_name": "XD Incentives",
      "sent_at": "2025-01-15T10:30:00Z",
      "delivered_at": "2025-01-15T10:31:00Z",
      "read_at": "2025-01-15T10:35:00Z",
      "failed_at": null,
      "scheduled_for": null,
      "created": "2025-01-15T10:30:00Z",
      "modified": "2025-01-15T10:35:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total_count": 105,
    "total_pages": 6,
    "has_next": true,
    "has_previous": false,
    "next_page": 2,
    "previous_page": null
  },
  "statistics": {
    "total_communications": 105,
    "status_distribution": {
      "sent": 23,
      "delivered": 19,
      "read": 17,
      "pending": 23,
      "failed": 23
    },
    "type_distribution": {
      "email": 21,
      "notification": 21,
      "message": 21,
      "sms": 21,
      "push": 21
    }
  },
  "filters": {
    "type": "email",
    "status": "sent",
    "priority": null,
    "category": null,
    "to_member": null,
    "from_member": null
  }
}
```

---

## Member Hierarchy Management APIs

**⚠️ Admin Only:** All hierarchy management endpoints require admin permissions (`is_superuser` or `member.member.manager` permission). Regular managers cannot edit hierarchy relationships.

### Get Member Hierarchy
**Endpoint:** `GET /api/member/{member_id}/hierarchy-management/`

**Description:** Get all hierarchy relationships for a specific member (managers and subordinates)

**Permissions:** Admin only

**Headers:**
```
Authorization: Bearer <your_access_token>
```

**Example:**
```bash
curl -X GET "http://localhost:8000/api/member/4/hierarchy-management/" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "member": {
    "id": 4,
    "username": "mike.wilson",
    "full_name": "Michael Wilson",
    "email": "<EMAIL>"
  },
  "managers": [
    {
      "id": 8,
      "manager": {
        "id": 2,
        "username": "john.doe",
        "full_name": "John Doe",
        "email": "<EMAIL>"
      },
      "relationship_type": "direct_manager",
      "is_primary": true,
      "start_date": "2024-01-01",
      "end_date": null,
      "notes": "Sample hierarchy relationship",
      "is_active": true,
      "created": "2025-07-17T21:05:46.694079Z",
      "modified": "2025-07-17T21:05:46.694086Z"
    }
  ],
  "subordinates": [],
  "statistics": {
    "total_managers": 1,
    "active_managers": 1,
    "primary_managers": 1,
    "total_subordinates": 0,
    "active_subordinates": 0,
    "primary_subordinates": 0
  }
}
```

### Add Hierarchy Relationship
**Endpoint:** `POST /api/member/{member_id}/hierarchy-management/`

**Description:** Add a new hierarchy relationship for a member

**Permissions:** Admin only

**Headers:**
```
Authorization: Bearer <your_access_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "manager_id": 5,
  "relationship_type": "mentor",
  "is_primary": false,
  "start_date": "2024-01-01",
  "end_date": null,
  "notes": "Added via API"
}
```

**Example:**
```bash
curl -X POST "http://localhost:8000/api/member/4/hierarchy-management/" \
  -H "Authorization: Bearer <your_access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "manager_id": 5,
    "relationship_type": "mentor",
    "is_primary": false,
    "start_date": "2024-01-01",
    "end_date": null,
    "notes": "Added via API"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Hierarchy relationship created successfully",
  "hierarchy": {
    "id": 13,
    "member": {
      "id": 4,
      "username": "mike.wilson",
      "full_name": "Michael Wilson"
    },
    "manager": {
      "id": 5,
      "username": "dcostello",
      "full_name": "Daniel Costello"
    },
    "relationship_type": "mentor",
    "is_primary": false,
    "start_date": "2024-01-01",
    "end_date": null,
    "notes": "Added via API",
    "is_active": true
  }
}
```

### Get Specific Hierarchy Relationship
**Endpoint:** `GET /api/hierarchy/{hierarchy_id}/`

**Description:** Get details of a specific hierarchy relationship

**Permissions:** Admin only

**Headers:**
```
Authorization: Bearer <your_access_token>
```

**Example:**
```bash
curl -X GET "http://localhost:8000/api/hierarchy/13/" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "id": 13,
  "member": {
    "id": 4,
    "username": "mike.wilson",
    "full_name": "Michael Wilson",
    "email": "<EMAIL>"
  },
  "manager": {
    "id": 5,
    "username": "dcostello",
    "full_name": "Daniel Costello",
    "email": "<EMAIL>"
  },
  "relationship_type": "mentor",
  "is_primary": true,
  "start_date": null,
  "end_date": null,
  "notes": "Updated via API",
  "is_active": true,
  "created": "2025-07-22T15:20:38.010749Z",
  "modified": "2025-07-22T15:25:14.633641Z"
}
```

### Update Hierarchy Relationship
**Endpoint:** `PUT /api/hierarchy/{hierarchy_id}/`

**Description:** Update a specific hierarchy relationship

**Permissions:** Admin only

**Headers:**
```
Authorization: Bearer <your_access_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "relationship_type": "mentor",
  "is_primary": true,
  "start_date": "2024-01-01",
  "end_date": null,
  "notes": "Updated relationship"
}
```

**Example:**
```bash
curl -X PUT "http://localhost:8000/api/hierarchy/13/" \
  -H "Authorization: Bearer <your_access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "relationship_type": "mentor",
    "is_primary": true,
    "start_date": "2024-01-01",
    "end_date": null,
    "notes": "Updated relationship"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Hierarchy relationship updated successfully",
  "hierarchy": {
    "id": 13,
    "member": {
      "id": 4,
      "username": "mike.wilson",
      "full_name": "Michael Wilson"
    },
    "manager": {
      "id": 5,
      "username": "dcostello",
      "full_name": "Daniel Costello"
    },
    "relationship_type": "mentor",
    "is_primary": true,
    "start_date": "2024-01-01",
    "end_date": null,
    "notes": "Updated relationship",
    "is_active": true
  }
}
```

### Partial Update Hierarchy Relationship
**Endpoint:** `PATCH /api/hierarchy/{hierarchy_id}/`

**Description:** Partially update a hierarchy relationship

**Permissions:** Admin only

**Headers:**
```
Authorization: Bearer <your_access_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "notes": "Updated via API",
  "is_primary": true
}
```

**Example:**
```bash
curl -X PATCH "http://localhost:8000/api/hierarchy/13/" \
  -H "Authorization: Bearer <your_access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "notes": "Updated via API",
    "is_primary": true
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Hierarchy relationship partially updated successfully",
  "hierarchy": {
    "id": 13,
    "member": {
      "id": 4,
      "username": "mike.wilson",
      "full_name": "Michael Wilson"
    },
    "manager": {
      "id": 5,
      "username": "dcostello",
      "full_name": "Daniel Costello"
    },
    "relationship_type": "mentor",
    "is_primary": true,
    "start_date": null,
    "end_date": null,
    "notes": "Updated via API",
    "is_active": true
  }
}
```

### Delete Hierarchy Relationship
**Endpoint:** `DELETE /api/hierarchy/{hierarchy_id}/`

**Description:** Delete a specific hierarchy relationship

**Permissions:** Admin only

**Headers:**
```
Authorization: Bearer <your_access_token>
```

**Example:**
```bash
curl -X DELETE "http://localhost:8000/api/hierarchy/13/" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "success": true,
  "message": "Hierarchy relationship deleted successfully",
  "deleted_relationship": {
    "member": {
      "id": 4,
      "username": "mike.wilson",
      "full_name": "Michael Wilson"
    },
    "manager": {
      "id": 5,
      "username": "dcostello",
      "full_name": "Daniel Costello"
    },
    "relationship_type": "mentor",
    "is_primary": true
  }
}
```

### Bulk Create Hierarchy Relationships
**Endpoint:** `POST /api/hierarchy/bulk/`

**Description:** Create multiple hierarchy relationships at once

**Permissions:** Admin only

**Headers:**
```
Authorization: Bearer <your_access_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "relationships": [
    {
      "member_id": 3,
      "manager_id": 2,
      "relationship_type": "direct_manager",
      "is_primary": true,
      "notes": "Bulk created"
    },
    {
      "member_id": 3,
      "manager_id": 5,
      "relationship_type": "mentor",
      "is_primary": false,
      "notes": "Bulk created mentor"
    }
  ]
}
```

**Example:**
```bash
curl -X POST "http://localhost:8000/api/hierarchy/bulk/" \
  -H "Authorization: Bearer <your_access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "relationships": [
      {
        "member_id": 3,
        "manager_id": 2,
        "relationship_type": "direct_manager",
        "is_primary": true,
        "notes": "Bulk created"
      },
      {
        "member_id": 3,
        "manager_id": 5,
        "relationship_type": "mentor",
        "is_primary": false,
        "notes": "Bulk created mentor"
      }
    ]
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Bulk hierarchy creation completed. 2 created, 0 errors.",
  "created_relationships": [
    {
      "id": 14,
      "member": {
        "id": 3,
        "username": "jane.smith",
        "full_name": "Jane Smith"
      },
      "manager": {
        "id": 2,
        "username": "john.doe",
        "full_name": "John Doe"
      },
      "relationship_type": "direct_manager",
      "is_primary": true
    },
    {
      "id": 15,
      "member": {
        "id": 3,
        "username": "jane.smith",
        "full_name": "Jane Smith"
      },
      "manager": {
        "id": 5,
        "username": "dcostello",
        "full_name": "Daniel Costello"
      },
      "relationship_type": "mentor",
      "is_primary": false
    }
  ],
  "errors": [],
  "summary": {
    "total_requested": 2,
    "created": 2,
    "errors": 0
  }
}
```

### Bulk Delete Hierarchy Relationships
**Endpoint:** `DELETE /api/hierarchy/bulk/`

**Description:** Delete multiple hierarchy relationships at once

**Permissions:** Admin only

**Headers:**
```
Authorization: Bearer <your_access_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "hierarchy_ids": [14, 15]
}
```

**Example:**
```bash
curl -X DELETE "http://localhost:8000/api/hierarchy/bulk/" \
  -H "Authorization: Bearer <your_access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "hierarchy_ids": [14, 15]
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Bulk hierarchy deletion completed. 2 deleted, 0 errors.",
  "deleted_count": 2,
  "errors": [],
  "summary": {
    "total_requested": 2,
    "deleted": 2,
    "errors": 0
  }
}
```

### Search Hierarchy Relationships
**Endpoint:** `GET /api/hierarchy/search/`

**Description:** Search hierarchy relationships with various filters

**Permissions:** Admin only

**Query Parameters:**
- `member_id` (integer): Filter by member ID
- `manager_id` (integer): Filter by manager ID
- `relationship_type` (string): Filter by relationship type
- `is_primary` (boolean): Filter by primary status (true/false)
- `is_active` (boolean): Filter by active status (true/false)
- `page` (integer): Page number (default: 1)
- `page_size` (integer): Items per page (default: 20, max: 100)

**Example:**
```bash
curl -X GET "http://localhost:8000/api/hierarchy/search/?member_id=4&is_active=true" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "hierarchies": [
    {
      "id": 13,
      "member": {
        "id": 4,
        "username": "mike.wilson",
        "full_name": "Michael Wilson",
        "email": "<EMAIL>"
      },
      "manager": {
        "id": 5,
        "username": "dcostello",
        "full_name": "Daniel Costello",
        "email": "<EMAIL>"
      },
      "relationship_type": "mentor",
      "is_primary": true,
      "start_date": null,
      "end_date": null,
      "notes": "Updated via API",
      "is_active": true,
      "created": "2025-07-22T15:20:38.010749Z",
      "modified": "2025-07-22T15:25:14.633641Z"
    }
  ],
  "pagination": {
    "page": 1,
    "pages": 1,
    "total": 1,
    "has_next": false,
    "has_previous": false
  },
  "filters": {
    "member_id": "4",
    "manager_id": "",
    "relationship_type": "",
    "is_primary": "",
    "is_active": "true"
  }
}
```

---

## Error Handling

All API endpoints return consistent error responses:

### Authentication Error (401)
```json
{
  "detail": "Authentication credentials were not provided."
}
```

### Permission Error (403)
```json
{
  "error": "Access denied. Admin permission required."
}
```

### Not Found Error (404)
```json
{
  "error": "Member not found."
}
```

### Validation Error (400)
```json
{
  "error": "Invalid data provided.",
  "details": {
    "field_name": ["This field is required."]
  }
}
```

### Server Error (500)
```json
{
  "error": "Internal server error occurred."
}
```

---

## Password Reset APIs

### Request Password Reset
**Endpoint:** `POST /api/password-reset/request/`

**Description:** Request a password reset for a member by email or username

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```
or
```json
{
  "username": "username"
}
```

**Example:**
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}' \
  http://localhost:8000/api/password-reset/request/
```

**Response:**
```json
{
  "success": true,
  "message": "Password reset link has been generated.",
  "reset_link": "http://localhost:8000/reset-password/abc123.../",
  "member": {
    "id": 1,
    "username": "user",
    "email": "<EMAIL>"
  }
}
```

### Validate Password Reset Token
**Endpoint:** `POST /api/password-reset/validate/`

**Description:** Validate a password reset token before allowing password change

**Request Body:**
```json
{
  "token": "your_reset_token"
}
```

**Example:**
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"token": "your_reset_token"}' \
  http://localhost:8000/api/password-reset/validate/
```

**Response:**
```json
{
  "success": true,
  "message": "Reset token is valid",
  "member": {
    "id": 1,
    "username": "user",
    "email": "<EMAIL>"
  },
  "reset_id": 1
}
```

### Confirm Password Reset
**Endpoint:** `POST /api/password-reset/confirm/`

**Description:** Complete password reset with new password

**Request Body:**
```json
{
  "token": "your_reset_token",
  "new_password": "newpassword123",
  "confirm_password": "newpassword123"
}
```

**Example:**
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "token": "your_reset_token",
    "new_password": "newpassword123",
    "confirm_password": "newpassword123"
  }' \
  http://localhost:8000/api/password-reset/confirm/
```

**Response:**
```json
{
  "success": true,
  "message": "Password has been successfully reset",
  "member": {
    "id": 1,
    "username": "user",
    "email": "<EMAIL>"
  }
}
```

### Get Password Reset History (Admin Only)
**Endpoint:** `GET /api/password-reset/history/`

**Description:** Get password reset history for all members (admin only)

**Query Parameters:**
- `page` (integer): Page number (default: 1)
- `limit` (integer): Items per page (default: 20)

**Example:**
```bash
curl -X GET "http://localhost:8000/api/password-reset/history/?page=1&limit=20" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "password_resets": [
    {
      "id": 1,
      "member": {
        "id": 1,
        "username": "user",
        "email": "<EMAIL>",
        "name": "John Doe"
      },
      "link": "http://localhost:8000/reset-password/abc123.../",
      "created": "2025-01-15T10:30:00Z",
      "modified": "2025-01-15T10:30:00Z",
      "is_expired": false
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total_count": 45,
    "total_pages": 3,
    "has_next": true,
    "has_previous": false,
    "next_page": 2,
    "previous_page": null
  }
}
```

### Get Password Reset Statistics (Admin Only)
**Endpoint:** `GET /api/password-reset/stats/`

**Description:** Get password reset statistics and analytics (admin only)

**Example:**
```bash
curl -X GET http://localhost:8000/api/password-reset/stats/ \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "statistics": {
    "total_resets": 150,
    "today_resets": 5,
    "week_resets": 25,
    "month_resets": 100,
    "expired_resets": 50,
    "active_resets": 100
  },
  "top_members": [
    {
      "username": "user1",
      "email": "<EMAIL>",
      "reset_count": 5
    },
    {
      "username": "user2",
      "email": "<EMAIL>",
      "reset_count": 3
    }
  ]
}
```

---

## Member Profile Management APIs

### Get Current Member Profile
**Endpoint:** `GET /api/member/profile/`

**Description:** Get current member's full profile with all details

**Headers:**
```
Authorization: Bearer <your_access_token>
```

**Response:**
```json
{
  "id": 4,
  "username": "mike.wilson",
  "email": "<EMAIL>",
  "first_name": "Michael",
  "last_name": "Wilson",
  "full_name": "Michael Wilson",
  "status": 3,
  "tier": 2,
  "lang": 1,
  "two_factor_auth_method": 1,
  "feature_flags": [],
  "notes": "Updated via API",
  "member_type": {
    "id": 3,
    "name": "Sales Rep",
    "slug": "regular",
    "description": "Standard member with basic access"
  },
  "region": {
    "id": 3,
    "title": "Asia Pacific",
    "can_signup": true
  },
  "work_address": {
    "address1": null,
    "address2": null,
    "city": "Tokyo",
    "state": "JP",
    "postal": null,
    "country": "usa"
  },
  "home_address": {
    "address1": null,
    "address2": null,
    "city": null,
    "state": null,
    "postal": null,
    "country": "usa"
  },
  "contact_info": {
    "contact_at_work": false,
    "phone_work": null,
    "phone_home": null,
    "phone_cell": "+81-3-9876-5432"
  },
  "teams": [
    {
      "id": 3,
      "name": "Distributor 1",
      "team_type": "distributor",
      "role": "salesrep",
      "is_primary": true,
      "start_date": "2024-01-01",
      "end_date": null
    }
  ],
  "managers": [
    {
      "id": 2,
      "username": "john.doe",
      "full_name": "John Doe",
      "email": "<EMAIL>",
      "relationship_type": "direct_manager",
      "is_primary": true,
      "start_date": "2024-01-01",
      "end_date": null
    }
  ],
  "subordinates": [],
  "terms_acceptance": {
    "has_accepted_current": true,
    "current_terms_id": 2,
    "current_terms_title": "XD Incentives Terms and Conditions v1.0",
    "last_accepted_terms": "XD Incentives Terms and Conditions v1.0"
  },
  "privacy_acceptance": {
    "has_accepted_current": true,
    "current_privacy_id": 2,
    "current_privacy_title": "XD Incentives Privacy Policy v1.0",
    "last_accepted_privacy": "XD Incentives Privacy Policy v1.0"
  },
  "is_active": true,
  "is_staff": false,
  "is_superuser": false,
  "date_joined": "2025-07-11T19:36:05.027981Z",
  "last_login": "2025-07-18T18:46:00.637741Z",
  "created": "2025-07-11T19:36:05.039651Z",
  "modified": "2025-07-22T14:20:52.113184Z"
}
```

### Update Current Member Profile
**Endpoint:** `PUT /api/member/profile/`

**Description:** Update current member's profile (members can only update their own profiles)

**Headers:**
```
Authorization: Bearer <your_access_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "first_name": "Michael",
  "last_name": "Wilson",
  "email": "<EMAIL>",
  "work_address1": "123 Business St",
  "work_city": "Tokyo",
  "work_state": "JP",
  "phone_cell": "+81-3-9876-5432",
  "notes": "Updated profile information"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "member": {
    "id": 4,
    "username": "mike.wilson",
    "email": "<EMAIL>",
    "full_name": "Michael Wilson"
  }
}
```

### Partial Update Current Member Profile
**Endpoint:** `PATCH /api/member/profile/`

**Description:** Partial update of current member's profile

**Headers:**
```
Authorization: Bearer <your_access_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "first_name": "Michael",
  "phone_cell": "+81-3-9876-5432"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Profile partially updated successfully",
  "member": {
    "id": 4,
    "username": "mike.wilson",
    "email": "<EMAIL>",
    "full_name": "Michael Wilson"
  }
}
```

### Get Specific Member Profile (Admin Only)
**Endpoint:** `GET /api/member/profile/{member_id}/`

**Description:** Get specific member's profile (admin only)

**Headers:**
```
Authorization: Bearer <your_access_token>
```

**Response:** Same as current member profile but includes additional admin-only fields:
```json
{
  // ... same fields as current member profile ...
  "approval": {
    "approved_by": {
      "id": 2,
      "username": "john.doe",
      "full_name": "John Doe"
    },
    "approved_date": "2025-07-15T10:30:00Z",
    "denied_by": null,
    "denied_date": null
  }
}
```

### Update Specific Member Profile (Admin Only)
**Endpoint:** `PUT /api/member/profile/{member_id}/`

**Description:** Update specific member's profile (admin only)

**Headers:**
```
Authorization: Bearer <your_access_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "username": "mike.wilson",
  "email": "<EMAIL>",
  "first_name": "Michael",
  "last_name": "Wilson",
  "status": 3,
  "tier": 2,
  "member_type": 3,
  "region": 3,
  "is_active": true,
  "is_staff": false,
  "is_superuser": false
}
```

**Response:**
```json
{
  "success": true,
  "message": "Member profile updated successfully",
  "member": {
    "id": 4,
    "username": "mike.wilson",
    "email": "<EMAIL>",
    "full_name": "Michael Wilson"
  }
}
```

### Partial Update Specific Member Profile (Admin Only)
**Endpoint:** `PATCH /api/member/profile/{member_id}/`

**Description:** Partial update of specific member's profile (admin only)

**Headers:**
```
Authorization: Bearer <your_access_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "status": 3,
  "tier": 2,
  "notes": "Updated by admin via API"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Member profile partially updated successfully",
  "member": {
    "id": 4,
    "username": "mike.wilson",
    "email": "<EMAIL>",
    "full_name": "Michael Wilson"
  }
}
```

### Search Members (Admin Only)
**Endpoint:** `GET /api/member/profile/search/`

**Description:** Search members with various filters (admin only)

**Query Parameters:**
- `search` (string): Search in username, email, first_name, last_name, phone fields
- `member_type` (integer): Filter by member type ID
- `region` (integer): Filter by region ID
- `status` (integer): Filter by status
- `team` (integer): Filter by team ID
- `is_active` (boolean): Filter by active status (true/false)
- `page` (integer): Page number (default: 1)
- `page_size` (integer): Items per page (default: 20, max: 100)

**Example:**
```bash
curl -X GET "http://localhost:8000/api/member/profile/search/?search=mike&member_type=3&is_active=true&page=1&page_size=10" \
  -H "Authorization: Bearer <your_access_token>"
```

**Response:**
```json
{
  "members": [
    {
      "id": 4,
      "username": "mike.wilson",
      "email": "<EMAIL>",
      "first_name": "Michael",
      "last_name": "Wilson",
      "full_name": "Michael Wilson",
      "status": 3,
      "tier": 2,
      "member_type": {
        "id": 3,
        "name": "Sales Rep",
        "slug": "regular"
      },
      "region": {
        "id": 3,
        "title": "Asia Pacific"
      },
      "phone_cell": "+81-3-9876-5432",
      "phone_work": null,
      "is_active": true,
      "is_staff": false,
      "is_superuser": false,
      "date_joined": "2025-07-11T19:36:05.027981Z",
      "last_login": "2025-07-18T18:46:00.637741Z",
      "created": "2025-07-11T19:36:05.039651Z"
    }
  ],
  "pagination": {
    "page": 1,
    "pages": 1,
    "total": 1,
    "has_next": false,
    "has_previous": false
  },
  "filters": {
    "search": "mike",
    "member_type": "3",
    "region": "",
    "status": "",
    "team": "",
    "is_active": "true"
  }
}
```

---

## Rate Limiting

API endpoints are subject to rate limiting:

- **Authentication endpoints**: 5 requests per minute
- **Read endpoints**: 100 requests per minute
- **Write endpoints**: 20 requests per minute
- **Admin endpoints**: 50 requests per minute

When rate limited, the API returns:

```json
{
  "error": "Rate limit exceeded. Please try again later.",
  "retry_after": 60
}
```

---

## Data Types

### Communication Types
- `email`: Email communications
- `notification`: System notifications
- `message`: Direct messages
- `sms`: SMS messages
- `push`: Push notifications

### Communication Status
- `pending`: Awaiting processing
- `sent`: Successfully sent
- `delivered`: Delivered to recipient
- `read`: Read by recipient
- `failed`: Failed to send
- `cancelled`: Cancelled

### Communication Priority
- `low`: Low priority
- `normal`: Normal priority
- `high`: High priority
- `urgent`: Urgent priority

### Member Status
- `1`: Active
- `2`: Inactive
- `3`: Pending approval
- `4`: Suspended

### Team Roles
- `admin`: Team administrator
- `contributor`: Team contributor
- `manager`: Team manager
- `member`: Team member
- `observer`: Team observer
- `lead`: Team lead
- `salesrep`: Sales representative

---

## Testing

### Test API Endpoint
**Endpoint:** `GET /api/hello/`

**Description:** Simple test endpoint to verify authentication

**Example:**
```bash
curl -X GET "http://localhost:8000/api/hello/" \
  -H "Authorization: Bearer <your_access_token>"
```

**Success Response:**
```json
{
  "message": "Hello, World!",
  "user": "john_doe",
  "timestamp": "2025-01-15T10:30:00Z"
}
```

**Failure Response:**
```json
{
  "detail": "Authentication credentials were not provided."
}
```

---

## Notes

1. **Authentication**: All endpoints require valid JWT tokens except `/api/token/` and `/api/token/refresh/`
2. **Permissions**: Admin endpoints require appropriate permissions
3. **Pagination**: List endpoints support pagination with `page` and `limit` parameters
4. **Filtering**: Most list endpoints support various filters
5. **Search**: Search endpoints support field-specific queries
6. **Statistics**: Admin endpoints often include statistical data
7. **Timestamps**: All timestamps are in ISO 8601 format (UTC)
8. **IDs**: All IDs are integers
9. **Relationships**: Foreign key relationships are expanded in responses
10. **Error Handling**: Consistent error format across all endpoints 