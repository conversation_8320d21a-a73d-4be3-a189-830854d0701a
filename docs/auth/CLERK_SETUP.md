# Clerk Integration Setup Guide

This guide will help you integrate Clerk authentication into your Django application.

## 📋 Prerequisites

1. **Clerk Account**: Sign up at [clerk.com](https://clerk.com)
2. **Django Application**: Your existing Django app with the Member model
3. **Docker Environment**: Your current Docker setup

## 🚀 Step 1: Install Dependencies

The required dependencies have been added to `requirements.txt`:

```bash
# Clerk Authentication
clerk-sdk-python
django-clerk
```

Install the dependencies:

```bash
docker-compose exec web pip install -r requirements.txt
```

## 🔧 Step 2: Configure Environment Variables

Add the following Clerk configuration to your `.env` file:

```bash
# Clerk Configuration
CLERK_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
CLERK_SECRET_KEY=sk_test_your_secret_key_here
CLERK_API_URL=https://api.clerk.com/v1
CLERK_FRONTEND_API=https://clerk.your-domain.com
CLERK_JWT_ISSUER=https://clerk.your-domain.com
CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Clerk Settings
CLERK_SYNC_USERS=True
CLERK_SESSION_DURATION=86400
CLERK_MFA_ENABLED=True
CLERK_ORGANIZATIONS_ENABLED=True
CLERK_DEBUG=True
CLERK_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
```

## 🗄️ Step 3: Run Database Migrations

Apply the database migrations to add Clerk fields:

```bash
docker-compose exec web python manage.py migrate
```

## ⚙️ Step 4: Configure Clerk Dashboard

### 4.1 Create a Clerk Application

1. Go to [Clerk Dashboard](https://dashboard.clerk.com)
2. Create a new application
3. Note your **Publishable Key** and **Secret Key**

### 4.2 Configure JWT Settings

In your Clerk dashboard:

1. Go to **JWT Templates**
2. Create a new JWT template with the following claims:
   ```json
   {
     "sub": "{{user.id}}",
     "email": "{{user.primary_email_address.email}}",
     "given_name": "{{user.first_name}}",
     "family_name": "{{user.last_name}}",
     "preferred_username": "{{user.username}}",
     "phone_number": "{{user.primary_phone_number.phone_number}}",
     "picture": "{{user.image_url}}"
   }
   ```

### 4.3 Configure Webhooks

1. Go to **Webhooks** in your Clerk dashboard
2. Add a new webhook endpoint: `https://your-domain.com/api/clerk/webhook/`
3. Select the following events:
   - `user.created`
   - `user.updated`
   - `user.deleted`
4. Copy the webhook secret to your `.env` file

## 🔐 Step 5: Frontend Integration

### 5.1 Install Clerk Frontend SDK

```bash
npm install @clerk/clerk-react
# or
yarn add @clerk/clerk-react
```

### 5.2 Configure Clerk Provider

```jsx
import { ClerkProvider } from '@clerk/clerk-react';

const clerkPubKey = process.env.REACT_APP_CLERK_PUBLISHABLE_KEY;

function App() {
  return (
    <ClerkProvider publishableKey={clerkPubKey}>
      <YourApp />
    </ClerkProvider>
  );
}
```

### 5.3 Authenticate with Django API

```jsx
import { useAuth } from '@clerk/clerk-react';

function LoginComponent() {
  const { getToken } = useAuth();

  const authenticateWithDjango = async () => {
    try {
      const token = await getToken();
      
      const response = await fetch('/api/clerk/auth/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      const data = await response.json();
      
      if (data.success) {
        // Store Django JWT tokens
        localStorage.setItem('access_token', data.tokens.access);
        localStorage.setItem('refresh_token', data.tokens.refresh);
        
        // Redirect to dashboard
        window.location.href = '/dashboard';
      }
    } catch (error) {
      console.error('Authentication error:', error);
    }
  };

  return (
    <button onClick={authenticateWithDjango}>
      Sign In with Clerk
    </button>
  );
}
```

## 🧪 Step 6: Test the Integration

### 6.1 Test Authentication

```bash
# Test Clerk authentication endpoint
curl -X POST http://localhost:8000/api/clerk/auth/ \
  -H "Content-Type: application/json" \
  -d '{"token": "your_clerk_jwt_token"}'
```

### 6.2 Test User Sync

```bash
# Test user sync (requires authentication)
curl -X POST http://localhost:8000/api/clerk/sync/ \
  -H "Authorization: Bearer your_django_jwt_token"
```

### 6.3 Test Webhook

```bash
# Test webhook endpoint
curl -X POST http://localhost:8000/api/clerk/webhook/ \
  -H "Content-Type: application/json" \
  -H "Svix-Signature: your_webhook_signature" \
  -H "Svix-Timestamp: timestamp" \
  -d '{"type": "user.created", "data": {...}}'
```

## 📚 API Endpoints

### Authentication
- `POST /api/clerk/auth/` - Authenticate with Clerk token
- `POST /api/clerk/sync/` - Sync user data from Clerk
- `POST /api/clerk/webhook/` - Handle Clerk webhooks

### User Management
- All existing API endpoints work with Clerk-authenticated users
- Users are automatically created/updated from Clerk data
- Profile images and additional data are synced

## 🔧 Configuration Options

### Clerk Settings (`config/clerk_settings.py`)

```python
# User Model Mapping
CLERK_USER_MODEL_MAPPING = {
    'id': 'clerk_id',
    'email': 'email',
    'first_name': 'first_name',
    'last_name': 'last_name',
    'username': 'username',
    'phone_number': 'phone_cell',
    'image_url': 'profile_image',
    'created_at': 'date_joined',
    'updated_at': 'modified',
}
```

### Authentication Backend

The `ClerkAuthenticationBackend` automatically:
- Verifies JWT tokens with Clerk
- Creates/updates users from Clerk data
- Handles profile image URLs
- Maintains user synchronization

## 🚨 Security Considerations

1. **Environment Variables**: Never commit Clerk keys to version control
2. **Webhook Verification**: Always verify webhook signatures
3. **Token Expiration**: Handle token refresh properly
4. **CORS Configuration**: Configure allowed origins carefully
5. **Rate Limiting**: Implement rate limiting for auth endpoints

## 🔍 Troubleshooting

### Common Issues

1. **"Invalid token" error**
   - Check JWT template configuration in Clerk dashboard
   - Verify token format and claims

2. **"User not linked to Clerk" error**
   - Ensure user has `clerk_id` field populated
   - Check webhook configuration

3. **Webhook signature verification fails**
   - Verify webhook secret in environment variables
   - Check timestamp and signature headers

4. **CORS errors**
   - Add your frontend domain to `CLERK_ALLOWED_ORIGINS`
   - Check CORS middleware configuration

### Debug Mode

Enable debug mode in your `.env`:

```bash
CLERK_DEBUG=True
```

This will provide detailed logging for Clerk operations.

## 📖 Next Steps

1. **Customize User Fields**: Modify `CLERK_USER_MODEL_MAPPING` to match your needs
2. **Add Organization Support**: Configure Clerk organizations if needed
3. **Implement Role-Based Access**: Use Clerk user metadata for permissions
4. **Add Multi-Factor Authentication**: Configure MFA in Clerk dashboard
5. **Set Up Email Templates**: Customize Clerk email templates

## 🆘 Support

- [Clerk Documentation](https://clerk.com/docs)
- [Django Documentation](https://docs.djangoproject.com)
- [JWT Token Verification](https://clerk.com/docs/backend-requests/making/jwt-templates)

---

**Note**: This integration maintains compatibility with your existing Django authentication while adding Clerk as an additional authentication method. Users can still use traditional Django authentication alongside Clerk authentication. 