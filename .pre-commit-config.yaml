repos:
  # General file cleanup
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
        exclude: '.*\.md$'
      - id: end-of-file-fixer
        exclude: '.*\.md$'
      - id: check-yaml
      - id: check-json
      - id: check-merge-conflict
      - id: check-added-large-files
        args: ["--maxkb=1000"]
      - id: debug-statements
      - id: check-docstring-first
        files: '\.py$'

  # Python code formatting
  - repo: https://github.com/psf/black
    rev: 24.10.0
    hooks:
      - id: black
        language_version: python3.12
        exclude: "(migrations|node_modules|static|frontend)/"

  # Import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: ["--profile", "black"]
        exclude: "(migrations|node_modules|static|frontend)/"

  # TODO: Enable django-upgrade when we are ready to upgrade
  # Django upgrade disabled due to actual circular import issue in v1.22.0
  # Issue: ImportError: cannot import name 'ast_start_offset' from partially initialized module
  # Can be run manually via: make lint-fix (which uses Docker container)
  # - repo: https://github.com/adamchainz/django-upgrade
  #   rev: 1.22.0
  #   hooks:
  #     - id: django-upgrade
  #       args: [--target-version, "5.2"]
  #       exclude: '(migrations|node_modules|static|frontend)/'

  # Flake8 linting
  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        # Configuration is handled by .flake8 file
        additional_dependencies:
          - flake8-django==1.4
          - flake8-bugbear==23.12.2
          - flake8-comprehensions==3.15.0
          - flake8-docstrings==1.7.0
          - flake8-import-order==0.18.2
          - flake8-quotes==3.4.0
        exclude: "(migrations|node_modules|static|frontend)/"

  # Security scanning
  - repo: https://github.com/pycqa/bandit
    rev: 1.8.0
    hooks:
      - id: bandit
        args: ["-c", "backend/pyproject.toml"]
        additional_dependencies: ["bandit[toml]"]
        exclude: "(tests|migrations|node_modules|static|frontend)/"

  # MyPy type checking - basic configuration for pre-commit compatibility
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.13.0
    hooks:
      - id: mypy
        args:
          [
            --python-version=3.12,
            --ignore-missing-imports,
            --show-error-codes,
            --no-strict-optional,
            --disable-error-code=var-annotated,
            --disable-error-code=attr-defined,
            --disable-error-code=assignment,
            --disable-error-code=misc,
            --disable-error-code=valid-type,
          ]
        additional_dependencies:
          - types-requests==2.32.0.20241016
        exclude: "(migrations|node_modules|static|frontend)/"

  # Pylint basic static analysis - errors only mode for pre-commit
  - repo: https://github.com/pycqa/pylint
    rev: v3.3.4
    hooks:
      - id: pylint
        args:
          [
            --errors-only,
            --rcfile=backend/pyproject.toml,
            --disable=import-error,
          ]
        exclude: "(migrations|node_modules|static|frontend)/"

  # Frontend linting and formatting
  - repo: local
    hooks:
      # ESLint for frontend TypeScript/React files
      - id: eslint-frontend
        name: ESLint (frontend)
        entry: bash -c 'cd frontend && npm run lint'
        language: system
        files: '^frontend/.*\.(ts|tsx|js|jsx)$'
        pass_filenames: false

      # TypeScript type checking for frontend
      - id: typescript-check-frontend
        name: TypeScript Check (frontend)
        entry: bash -c 'cd frontend && npm run type-check'
        language: system
        files: '^frontend/.*\.(ts|tsx)$'
        pass_filenames: false

# Configuration
default_stages: [pre-commit]
fail_fast: false
default_language_version:
  python: python3.12
  node: 18.17.0
