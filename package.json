{"author": "Integrity STL", "description": "XD Incentives - Multi-tenant SaaS Platform for Sales Incentive Management", "devDependencies": {"lint-staged": "^15.2.0", "rollup": "^4.27.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["incentives", "sales", "saas", "django", "react", "typescript"], "license": "PRIVATE", "name": "xd-incentives", "private": true, "repository": {"type": "git", "url": "https://github.com/integritystl/xd-incentives"}, "scripts": {"build": "npm run build --workspace=frontend", "dev": "npm run dev --workspace=frontend", "format": "npm run format --workspace=frontend", "lint": "npm run lint --workspace=frontend", "lint:fix": "npm run lint:fix --workspace=frontend", "test": "npm run test --workspace=frontend"}, "version": "1.0.0", "workspaces": ["frontend", "backend"]}