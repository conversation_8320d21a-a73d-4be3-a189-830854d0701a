import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'

import { authService } from './auth'

// Mock storage
const mockSessionStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage,
})

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

// Mock fetch
global.fetch = vi.fn()

describe('AuthService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('setTokens', () => {
    it('stores tokens in appropriate storage', () => {
      const tokenData = {
        access_token: 'access123',
        refresh_token: 'refresh123',
        expires_at: Date.now() + 3600000, // 1 hour from now
      }

      authService.setTokens(tokenData)

      expect(mockSessionStorage.setItem).toHaveBeenCalledWith(
        'xd_access_token',
        'access123'
      )
      expect(mockSessionStorage.setItem).toHaveBeenCalledWith(
        'xd_token_expires',
        tokenData.expires_at.toString()
      )
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'xd_refresh_token',
        'refresh123'
      )
    })
  })

  describe('getAccessToken', () => {
    it('returns token when valid and not expired', () => {
      const futureTime = Date.now() + 3600000 // 1 hour from now
      mockSessionStorage.getItem
        .mockReturnValueOnce('access123') // access token
        .mockReturnValueOnce(futureTime.toString()) // expires at

      const token = authService.getAccessToken()
      expect(token).toBe('access123')
    })

    it('returns null when token is expired', () => {
      const pastTime = Date.now() - 3600000 // 1 hour ago
      mockSessionStorage.getItem
        .mockReturnValueOnce('access123') // access token
        .mockReturnValueOnce(pastTime.toString()) // expires at

      const token = authService.getAccessToken()
      expect(token).toBeNull()
      expect(mockSessionStorage.removeItem).toHaveBeenCalled()
      expect(mockLocalStorage.removeItem).toHaveBeenCalled()
    })

    it('returns null when no token exists', () => {
      mockSessionStorage.getItem.mockReturnValue(null)

      const token = authService.getAccessToken()
      expect(token).toBeNull()
    })

    it('returns null when no expiry time exists', () => {
      mockSessionStorage.getItem
        .mockReturnValueOnce('access123') // access token
        .mockReturnValueOnce(null) // expires at

      const token = authService.getAccessToken()
      expect(token).toBeNull()
    })
  })

  describe('getRefreshToken', () => {
    it('returns refresh token from localStorage', () => {
      mockLocalStorage.getItem.mockReturnValue('refresh123')

      const token = authService.getRefreshToken()
      expect(token).toBe('refresh123')
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('xd_refresh_token')
    })

    it('returns null when no refresh token exists', () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      const token = authService.getRefreshToken()
      expect(token).toBeNull()
    })
  })

  describe('clearTokens', () => {
    it('removes all tokens from storage', () => {
      authService.clearTokens()

      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('xd_access_token')
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('xd_token_expires')
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('xd_refresh_token')
    })
  })

  describe('isAuthenticated', () => {
    it('returns true when valid token exists', () => {
      const futureTime = Date.now() + 3600000
      mockSessionStorage.getItem
        .mockReturnValueOnce('access123')
        .mockReturnValueOnce(futureTime.toString())

      expect(authService.isAuthenticated()).toBe(true)
    })

    it('returns false when no valid token exists', () => {
      mockSessionStorage.getItem.mockReturnValue(null)

      expect(authService.isAuthenticated()).toBe(false)
    })
  })

  describe('refreshAccessToken', () => {
    it('refreshes token successfully', async () => {
      mockLocalStorage.getItem.mockReturnValue('refresh123')

      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({
          access_token: 'new_access_token',
          refresh_token: 'new_refresh_token',
          expires_in: 3600,
        }),
      }

      global.fetch = vi.fn().mockResolvedValue(mockResponse)

      const token = await authService.refreshAccessToken()

      expect(token).toBe('new_access_token')
      expect(fetch).toHaveBeenCalledWith('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh_token: 'refresh123' }),
      })
      expect(mockSessionStorage.setItem).toHaveBeenCalledWith(
        'xd_access_token',
        'new_access_token'
      )
    })

    it('returns null when no refresh token exists', async () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      const token = await authService.refreshAccessToken()
      expect(token).toBeNull()
      expect(fetch).not.toHaveBeenCalled()
    })

    it('clears tokens and returns null on failed refresh', async () => {
      mockLocalStorage.getItem.mockReturnValue('refresh123')

      const mockResponse = {
        ok: false,
        status: 401,
      }

      global.fetch = vi.fn().mockResolvedValue(mockResponse)

      const token = await authService.refreshAccessToken()

      expect(token).toBeNull()
      expect(mockSessionStorage.removeItem).toHaveBeenCalled()
      expect(mockLocalStorage.removeItem).toHaveBeenCalled()
    })

    it('handles network errors', async () => {
      mockLocalStorage.getItem.mockReturnValue('refresh123')
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'))

      const token = await authService.refreshAccessToken()

      expect(token).toBeNull()
      expect(mockSessionStorage.removeItem).toHaveBeenCalled()
      expect(mockLocalStorage.removeItem).toHaveBeenCalled()
    })
  })

  describe('getValidAccessToken', () => {
    it('returns existing valid token', async () => {
      const futureTime = Date.now() + 3600000
      mockSessionStorage.getItem
        .mockReturnValueOnce('access123')
        .mockReturnValueOnce(futureTime.toString())

      const token = await authService.getValidAccessToken()
      expect(token).toBe('access123')
    })

    it('attempts refresh when no valid token exists', async () => {
      // First call - no valid token
      mockSessionStorage.getItem.mockReturnValue(null)
      // Refresh token exists
      mockLocalStorage.getItem.mockReturnValue('refresh123')

      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({
          access_token: 'refreshed_token',
          expires_in: 3600,
        }),
      }

      global.fetch = vi.fn().mockResolvedValue(mockResponse)

      const token = await authService.getValidAccessToken()
      expect(token).toBe('refreshed_token')
    })

    it('returns null when refresh fails', async () => {
      mockSessionStorage.getItem.mockReturnValue(null)
      mockLocalStorage.getItem.mockReturnValue('refresh123')

      global.fetch = vi.fn().mockResolvedValue({ ok: false })

      const token = await authService.getValidAccessToken()
      expect(token).toBeNull()
    })
  })
})
