interface TokenData {
  access_token: string
  refresh_token: string
  expires_at: number
}

class AuthService {
  private readonly ACCESS_TOKEN_KEY = 'xd_access_token'
  private readonly REFRESH_TOKEN_KEY = 'xd_refresh_token'
  private readonly TOKEN_EXPIRES_KEY = 'xd_token_expires'

  // Use sessionStorage for access tokens (more secure than localStorage)
  private getSecureStorage(): Storage {
    return sessionStorage
  }

  // Use localStorage only for refresh tokens (persist across sessions)
  private getPersistentStorage(): Storage {
    return localStorage
  }

  setTokens(tokenData: TokenData): void {
    const secureStorage = this.getSecureStorage()
    const persistentStorage = this.getPersistentStorage()

    secureStorage.setItem(this.ACCESS_TOKEN_KEY, tokenData.access_token)
    secureStorage.setItem(this.TOKEN_EXPIRES_KEY, tokenData.expires_at.toString())
    persistentStorage.setItem(this.REFRESH_TOKEN_KEY, tokenData.refresh_token)
  }

  getAccessToken(): string | null {
    const token = this.getSecureStorage().getItem(this.ACCESS_TOKEN_KEY)
    const expiresAt = this.getSecureStorage().getItem(this.TOKEN_EXPIRES_KEY)

    if (!token || !expiresAt) {
      return null
    }

    // Check if token is expired
    const now = Date.now()
    if (now >= parseInt(expiresAt)) {
      this.clearTokens()
      return null
    }

    return token
  }

  getRefreshToken(): string | null {
    return this.getPersistentStorage().getItem(this.REFRESH_TOKEN_KEY)
  }

  clearTokens(): void {
    const secureStorage = this.getSecureStorage()
    const persistentStorage = this.getPersistentStorage()

    secureStorage.removeItem(this.ACCESS_TOKEN_KEY)
    secureStorage.removeItem(this.TOKEN_EXPIRES_KEY)
    persistentStorage.removeItem(this.REFRESH_TOKEN_KEY)
  }

  isAuthenticated(): boolean {
    return this.getAccessToken() !== null
  }

  async refreshAccessToken(): Promise<string | null> {
    const refreshToken = this.getRefreshToken()
    if (!refreshToken) {
      return null
    }

    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh_token: refreshToken }),
      })

      if (!response.ok) {
        this.clearTokens()
        return null
      }

      const data = await response.json()
      this.setTokens({
        access_token: data.access_token,
        refresh_token: data.refresh_token || refreshToken,
        expires_at: Date.now() + (data.expires_in * 1000),
      })

      return data.access_token
    } catch (error) {
      console.error('Token refresh failed:', error)
      this.clearTokens()
      return null
    }
  }

  // Method to handle token expiration with automatic refresh
  async getValidAccessToken(): Promise<string | null> {
    let token = this.getAccessToken()

    if (!token) {
      // Try to refresh
      token = await this.refreshAccessToken()
    }

    return token
  }
}

export const authService = new AuthService()
export default authService
