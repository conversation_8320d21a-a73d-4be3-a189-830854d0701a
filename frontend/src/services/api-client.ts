interface RequestConfig extends RequestInit {
  params?: Record<string, string>
  data?: unknown
}

interface ApiResponse<T = unknown> {
  data: T
  status: number
  headers: Headers
}

type RequestInterceptor = (config: RequestConfig) => RequestConfig | Promise<RequestConfig>
type ResponseInterceptor<T = unknown> = (response: ApiResponse<T>) => ApiResponse<T> | Promise<ApiResponse<T>>

class ApiClient {
  private baseURL: string
  private defaultHeaders: HeadersInit
  private requestInterceptors: RequestInterceptor[] = []
  private responseInterceptors: ResponseInterceptor[] = []

  constructor(baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api') {
    this.baseURL = baseURL
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    }
  }

  private async getAuthToken(): Promise<string | null> {
    const { authService } = await import('./auth')
    return authService.getValidAccessToken()
  }

  private buildURL(endpoint: string, params?: Record<string, string>): string {
    const url = new URL(`${this.baseURL}${endpoint}`)
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value)
      })
    }
    return url.toString()
  }

  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    const contentType = response.headers.get('content-type')
    let data: T

    if (contentType?.includes('application/json')) {
      data = await response.json()
    } else {
      data = await response.text() as T
    }

    if (!response.ok) {
      throw {
        status: response.status,
        statusText: response.statusText,
        data,
        headers: response.headers,
      }
    }

    return {
      data,
      status: response.status,
      headers: response.headers,
    }
  }

  async request<T = unknown>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    let requestConfig = { ...config }

    // Apply request interceptors
    for (const interceptor of this.requestInterceptors) {
      requestConfig = await interceptor(requestConfig)
    }

    const { params, data, headers: customHeaders, ...fetchConfig } = requestConfig

    const url = this.buildURL(endpoint, params)
    const token = await this.getAuthToken()

    const headers = {
      ...this.defaultHeaders,
      ...customHeaders,
      ...(token && { Authorization: `Bearer ${token}` }),
    }

    const finalConfig: RequestInit = {
      ...fetchConfig,
      headers,
      ...(data ? { body: JSON.stringify(data) } : {}),
    }

    try {
      const response = await fetch(url, finalConfig)
      let apiResponse = await this.handleResponse<T>(response)

      // Apply response interceptors
      for (const interceptor of this.responseInterceptors) {
        apiResponse = await interceptor(apiResponse) as ApiResponse<T>
      }

      return apiResponse
    } catch (error) {
      if (error instanceof Error) {
        throw {
          message: error.message,
          name: error.name,
          stack: error.stack,
        }
      }
      throw error
    }
  }

  async get<T = unknown>(endpoint: string, config?: RequestConfig): Promise<T> {
    const response = await this.request<T>(endpoint, {
      ...config,
      method: 'GET',
    })
    return response.data
  }

  async post<T = unknown>(
    endpoint: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<T> {
    const response = await this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      data,
    })
    return response.data
  }

  async put<T = unknown>(
    endpoint: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<T> {
    const response = await this.request<T>(endpoint, {
      ...config,
      method: 'PUT',
      data,
    })
    return response.data
  }

  async patch<T = unknown>(
    endpoint: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<T> {
    const response = await this.request<T>(endpoint, {
      ...config,
      method: 'PATCH',
      data,
    })
    return response.data
  }

  async delete<T = unknown>(endpoint: string, config?: RequestConfig): Promise<T> {
    const response = await this.request<T>(endpoint, {
      ...config,
      method: 'DELETE',
    })
    return response.data
  }

  async setAuthToken(tokenData: { access_token: string; refresh_token: string; expires_in: number }): Promise<void> {
    const { authService } = await import('./auth')
    authService.setTokens({
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token,
      expires_at: Date.now() + (tokenData.expires_in * 1000),
    })
  }

  async clearAuthToken(): Promise<void> {
    const { authService } = await import('./auth')
    authService.clearTokens()
  }

  async isAuthenticated(): Promise<boolean> {
    const { authService } = await import('./auth')
    return authService.isAuthenticated()
  }

  addRequestInterceptor(interceptor: RequestInterceptor): () => void {
    this.requestInterceptors.push(interceptor)

    // Return a function to remove the interceptor
    return () => {
      const index = this.requestInterceptors.indexOf(interceptor)
      if (index !== -1) {
        this.requestInterceptors.splice(index, 1)
      }
    }
  }

  addResponseInterceptor<T = unknown>(interceptor: ResponseInterceptor<T>): () => void {
    this.responseInterceptors.push(interceptor as ResponseInterceptor)

    // Return a function to remove the interceptor
    return () => {
      const index = this.responseInterceptors.indexOf(interceptor as ResponseInterceptor)
      if (index !== -1) {
        this.responseInterceptors.splice(index, 1)
      }
    }
  }
}

export const apiClient = new ApiClient()

// Add automatic token refresh on 401 errors
apiClient.addResponseInterceptor(async (response) => {
  if (response.status === 401) {
    const { authService } = await import('./auth')
    const refreshedToken = await authService.refreshAccessToken()

    if (!refreshedToken) {
      // Redirect to login or handle unauthorized access
      window.location.href = '/login'
      throw new Error('Authentication required')
    }
  }

  return response
})

export default ApiClient
