import { getCurrentWebVitals } from './webVitals'

interface PerformanceMetrics {
  webVitals: Record<string, number>
  resourceTimings: {
    scripts: number
    styles: number
    images: number
    fonts: number
    total: number
  }
  memoryUsage?: {
    usedJSHeapSize: number
    totalJSHeapSize: number
    jsHeapSizeLimit: number
  }
  bundleSize: {
    js: number
    css: number
    total: number
  }
}

// Measure resource loading times
function getResourceTimings() {
  const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]

  const timings = {
    scripts: 0,
    styles: 0,
    images: 0,
    fonts: 0,
    total: 0,
  }

  resources.forEach((resource) => {
    const duration = resource.responseEnd - resource.startTime
    timings.total += duration

    if (resource.initiatorType === 'script') {
      timings.scripts += duration
    } else if (resource.initiatorType === 'css' || resource.initiatorType === 'link') {
      timings.styles += duration
    } else if (resource.initiatorType === 'img') {
      timings.images += duration
    } else if (resource.initiatorType === 'font') {
      timings.fonts += duration
    }
  })

  return timings
}

// Get memory usage (Chrome only)
function getMemoryUsage() {
  if ('memory' in performance) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const memory = (performance as any).memory
    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
    }
  }
  return undefined
}

// Calculate bundle sizes from loaded resources
function getBundleSize() {
  const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]

  let jsSize = 0
  let cssSize = 0

  resources.forEach((resource) => {
    if (resource.name.endsWith('.js') || resource.name.includes('.js?')) {
      jsSize += resource.transferSize || 0
    } else if (resource.name.endsWith('.css') || resource.name.includes('.css?')) {
      cssSize += resource.transferSize || 0
    }
  })

  return {
    js: jsSize,
    css: cssSize,
    total: jsSize + cssSize,
  }
}

// Run performance test
export async function runPerformanceTest(): Promise<PerformanceMetrics> {
  // Wait for page to fully load
  await new Promise(resolve => {
    if (document.readyState === 'complete') {
      resolve(undefined)
    } else {
      window.addEventListener('load', resolve)
    }
  })

  // Wait a bit more for async operations
  await new Promise(resolve => setTimeout(resolve, 2000))

  const [webVitals] = await Promise.all([
    getCurrentWebVitals(),
  ])

  return {
    webVitals,
    resourceTimings: getResourceTimings(),
    memoryUsage: getMemoryUsage(),
    bundleSize: getBundleSize(),
  }
}

// Format bytes to human readable
function formatBytes(bytes: number): string {
  if (bytes === 0) {return '0 B'}
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

// Generate performance report
export function generatePerformanceReport(metrics: PerformanceMetrics): string {
  const report = []

  report.push('=== Performance Report ===\n')

  // Web Vitals
  report.push('Core Web Vitals:')
  Object.entries(metrics.webVitals).forEach(([key, value]) => {
    const unit = key === 'CLS' ? '' : 'ms'
    report.push(`  ${key}: ${value.toFixed(2)}${unit}`)
  })

  // Resource Timings
  report.push('\nResource Loading Times:')
  report.push(`  Scripts: ${metrics.resourceTimings.scripts.toFixed(2)}ms`)
  report.push(`  Styles: ${metrics.resourceTimings.styles.toFixed(2)}ms`)
  report.push(`  Images: ${metrics.resourceTimings.images.toFixed(2)}ms`)
  report.push(`  Fonts: ${metrics.resourceTimings.fonts.toFixed(2)}ms`)
  report.push(`  Total: ${metrics.resourceTimings.total.toFixed(2)}ms`)

  // Bundle Size
  report.push('\nBundle Sizes:')
  report.push(`  JavaScript: ${formatBytes(metrics.bundleSize.js)}`)
  report.push(`  CSS: ${formatBytes(metrics.bundleSize.css)}`)
  report.push(`  Total: ${formatBytes(metrics.bundleSize.total)}`)

  // Memory Usage
  if (metrics.memoryUsage) {
    report.push('\nMemory Usage:')
    report.push(`  Used Heap: ${formatBytes(metrics.memoryUsage.usedJSHeapSize)}`)
    report.push(`  Total Heap: ${formatBytes(metrics.memoryUsage.totalJSHeapSize)}`)
    report.push(`  Heap Limit: ${formatBytes(metrics.memoryUsage.jsHeapSizeLimit)}`)
  }

  return report.join('\n')
}

// Console test runner
export async function testPerformance() {
  console.warn('Running performance test...')
  const metrics = await runPerformanceTest()
  const report = generatePerformanceReport(metrics)
  console.warn(report)
  return metrics
}

// Export for use in tests
if (import.meta.env.DEV) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (window as any).testPerformance = testPerformance
}
