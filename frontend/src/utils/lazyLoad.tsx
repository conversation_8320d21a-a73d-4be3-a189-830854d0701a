import React, { lazy, Suspense, type ComponentType } from 'react'

// Loading component with performance-focused skeleton
export const LoadingFallback = () => (
  <div className="animate-pulse">
    <div className="mb-4 w-3/4 h-8 bg-gray-200 rounded"></div>
    <div className="mb-2 w-full h-4 bg-gray-200 rounded"></div>
    <div className="w-5/6 h-4 bg-gray-200 rounded"></div>
  </div>
)

// Enhanced lazy loading with retry logic
// eslint-disable-next-line react-refresh/only-export-components
export function lazyLoadWithRetry<T extends ComponentType<unknown>>(
  importFunc: () => Promise<{ default: T }>,
  retries = 3,
  delay = 1000
): React.LazyExoticComponent<T> {
  return lazy(async () => {
    let lastError: unknown

    for (let i = 0; i < retries; i++) {
      try {
        return await importFunc()
      } catch (error) {
        lastError = error
        if (i < retries - 1) {
          await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
        }
      }
    }

    throw lastError
  })
}

// Wrapper for lazy-loaded components with Suspense
// eslint-disable-next-line react-refresh/only-export-components
export function withLazyLoad<P extends object>(
  Component: React.LazyExoticComponent<ComponentType<P>>,
  fallback?: React.ReactNode
) {
  const WrappedComponent = (props: P) => (
    <Suspense fallback={fallback || <LoadingFallback />}>
      <Component {...props} />
    </Suspense>
  )
  WrappedComponent.displayName = `withLazyLoad(${Component.displayName || Component.name || 'Component'})`
  return WrappedComponent
}

// Preload component for critical paths
// eslint-disable-next-line react-refresh/only-export-components
export function preloadComponent(
  importFunc: () => Promise<unknown>
) {
  // Start loading the component in the background
  importFunc()
}
