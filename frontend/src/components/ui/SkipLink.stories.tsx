import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite'

import { Ski<PERSON>Link, SkipLinks } from './SkipLink'

const meta = {
  title: 'Components/SkipLink',
  component: SkipLinks,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'Skip links provide keyboard navigation shortcuts for screen reader users and keyboard-only navigation.',
      },
    },
  },
  tags: ['autodocs'],
} satisfies Meta<typeof SkipLinks>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: () => (
    <div className="min-h-screen">
      <SkipLinks
        links={[
          { href: '#main-content', label: 'Skip to main content' },
          { href: '#navigation', label: 'Skip to navigation' },
          { href: '#footer', label: 'Skip to footer' },
        ]}
      />

      <div className="p-8">
        <div className="mb-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h3 className="font-semibold text-yellow-800 mb-2">Testing Skip Links</h3>
          <p className="text-sm text-yellow-700">
            <strong>To test:</strong> Click in this story area, then press Tab.
            The skip links will become visible when focused. Press Enter to navigate to different sections.
          </p>
        </div>

        <nav id="navigation" className="mb-8 p-4 bg-gray-100 rounded-lg">
          <h2 className="text-lg font-semibold mb-4">Navigation</h2>
          <ul className="space-y-2">
            <li><a href="#" className="text-blue-600 hover:underline">Home</a></li>
            <li><a href="#" className="text-blue-600 hover:underline">About</a></li>
            <li><a href="#" className="text-blue-600 hover:underline">Services</a></li>
            <li><a href="#" className="text-blue-600 hover:underline">Contact</a></li>
          </ul>
        </nav>

        <main id="main-content" className="mb-8">
          <h1 className="text-2xl font-bold mb-4">Main Content</h1>
          <p className="mb-4">
            This is the main content area. Skip links allow users to jump directly to this section
            without having to navigate through all the preceding content.
          </p>
          <p className="mb-4">
            This is particularly important for screen reader users and people who navigate using
            only the keyboard, as it saves them time and effort.
          </p>

          {/* Add some dummy content to demonstrate scrolling */}
          {Array.from({ length: 20 }, (_, i) => (
            <p key={i} className="mb-2">
              This is paragraph {i + 1} of the main content. It demonstrates how skip links
              help users avoid having to navigate through large amounts of content.
            </p>
          ))}
        </main>

        <footer id="footer" className="p-4 bg-gray-800 text-white rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Footer</h2>
          <p className="text-sm">
            This is the footer content. Users can skip directly here using the skip links.
          </p>
        </footer>
      </div>
    </div>
  ),
}

export const SingleSkipLink: Story = {
  render: () => (
    <div className="p-8">
      <div className="mb-4 p-4 bg-blue-50 rounded-lg">
        <p className="text-sm text-blue-800">
          Tab to focus the skip link below. It will become visible when focused.
        </p>
      </div>

      <SkipLink href="#target-section">
        Skip to target section
      </SkipLink>

      <div className="mt-8 space-y-4">
        <p>Some content that users might want to skip over...</p>
        <p>More content to demonstrate the skip functionality...</p>
        <p>Even more content that takes up space...</p>
      </div>

      <div id="target-section" className="mt-8 p-4 bg-green-100 rounded-lg">
        <h2 className="text-lg font-semibold text-green-800">Target Section</h2>
        <p className="text-green-700">
          This is where the skip link takes you. The skip link should be visible when focused
          and hidden otherwise.
        </p>
      </div>
    </div>
  ),
}

export const AccessibilityFeatures: Story = {
  render: () => (
    <div className="p-8">
      <div className="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">Accessibility Features</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Skip links are hidden visually but available to screen readers</li>
          <li>• They become visible when focused via keyboard navigation</li>
          <li>• Proper ARIA labeling for navigation landmarks</li>
          <li>• High contrast styling when visible</li>
          <li>• Focus management with proper ring indicators</li>
        </ul>
      </div>

      <SkipLinks
        links={[
          { href: '#content-1', label: 'Skip to first content section' },
          { href: '#content-2', label: 'Skip to second content section' },
          { href: '#content-3', label: 'Skip to third content section' },
        ]}
      />

      <section id="content-1" className="mb-8 p-6 bg-red-50 rounded-lg">
        <h2 className="text-xl font-semibold text-red-800 mb-3">First Content Section</h2>
        <p className="text-red-700">
          This is the first content section. Skip links help users navigate directly to specific
          sections without having to tab through all intervening content.
        </p>
      </section>

      <section id="content-2" className="mb-8 p-6 bg-green-50 rounded-lg">
        <h2 className="text-xl font-semibold text-green-800 mb-3">Second Content Section</h2>
        <p className="text-green-700">
          This is the second content section. Each skip link is properly labeled to describe
          where it will take the user.
        </p>
      </section>

      <section id="content-3" className="mb-8 p-6 bg-purple-50 rounded-lg">
        <h2 className="text-xl font-semibold text-purple-800 mb-3">Third Content Section</h2>
        <p className="text-purple-700">
          This is the third content section. The skip link styling ensures high contrast
          and visibility when focused.
        </p>
      </section>
    </div>
  ),
}
