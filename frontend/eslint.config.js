import js from '@eslint/js';
import typescript from '@typescript-eslint/eslint-plugin';
import typescriptParser from '@typescript-eslint/parser';
import react from 'eslint-plugin-react';
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';
import jsxA11y from 'eslint-plugin-jsx-a11y';
import importPlugin from 'eslint-plugin-import';
import storybook from 'eslint-plugin-storybook';
import globals from 'globals';

export default [
  // Global ignore patterns
  {
    ignores: [
      'dist/**',
      'node_modules/**',
      'build/**',
      '.eslintrc.json',
      '.eslintrc.json.backup',
      'src/*.gen.ts',
      'src/routeTree.gen.ts',
      '.storybook/main.ts'
    ]
  },

  // Base configuration for JavaScript files
  {
    ...js.configs.recommended,
    files: ['**/*.{js,jsx}']
  },

  // TypeScript and React configuration (app files only)
  {
    files: ['src/**/*.{js,jsx,ts,tsx}'],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        ecmaFeatures: {
          jsx: true
        },
        project: './tsconfig.app.json',
        tsconfigRootDir: import.meta.dirname,
      },
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.es2020
      }
    },
    plugins: {
      '@typescript-eslint': typescript,
      'react': react,
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
      'jsx-a11y': jsxA11y,
      'import': importPlugin
    },
    rules: {
      // TypeScript rules
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_'
        }
      ],
      ...typescript.configs.recommended.rules,

      // React rules
      'react/prop-types': 'off', // TypeScript provides type checking
      'react/react-in-jsx-scope': 'off',
      'react/jsx-uses-react': 'off',
      'react/jsx-uses-vars': 'error',
      ...react.configs.recommended.rules,
      ...react.configs['jsx-runtime'].rules,

      // React Hooks rules
      ...reactHooks.configs.recommended.rules,

      // React Refresh rules
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true }
      ],

      // Accessibility rules
      ...jsxA11y.configs.recommended.rules,
      'jsx-a11y/heading-has-content': 'off', // Allow dynamic content in headings

      // Import rules
      'import/no-cycle': 'error',
      'import/no-unresolved': 'off', // Turn off as TypeScript handles this better with path mapping
      'import/named': 'off', // Turn off as TypeScript handles this
      'import/default': 'off', // Turn off as TypeScript handles this
      'import/namespace': 'off', // Turn off as TypeScript handles this
      'import/order': [
        'warn',
        {
          alphabetize: {
            caseInsensitive: true,
            order: 'asc'
          },
          groups: [
            'builtin',
            'external',
            'internal',
            'parent',
            'sibling',
            'index'
          ],
          'newlines-between': 'always',
          pathGroups: [
            {
              pattern: '@/**',
              group: 'internal',
              position: 'before'
            }
          ],
          pathGroupsExcludedImportTypes: ['builtin']
        }
      ],

      // General code quality rules
      'curly': ['error', 'all'],
      'eqeqeq': ['error', 'always'],
      'no-console': [
        'warn',
        {
          allow: ['warn', 'error']
        }
      ],
      'no-var': 'error',
      'prefer-const': 'error'
    },
    settings: {
      react: {
        version: 'detect'
      },
      'import/resolver': {
        node: {
          extensions: ['.js', '.jsx', '.ts', '.tsx']
        },
        typescript: {
          project: ['./tsconfig.json', './tsconfig.app.json']
        }
      }
    }
  },

  // Configuration files (disable TypeScript project checking)
  {
    files: ['vite.config.ts', 'vitest.config.ts', 'postcss.config.js', 'eslint.config.js', '.storybook/**/*'],
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.es2020
      }
    },
    rules: {
      'curly': ['error', 'all'],
      'no-console': 'off',
      'no-undef': 'off',
      '@typescript-eslint/no-unused-vars': 'off'
    }
  },

  // Test files configuration
  {
    files: ['src/**/*.test.{ts,tsx}', 'src/**/*.spec.{ts,tsx}', 'src/test/**/*'],
    languageOptions: {
      globals: {
        ...globals.browser,
        vitest: 'readonly',
        vi: 'readonly',
        describe: 'readonly',
        it: 'readonly',
        test: 'readonly',
        expect: 'readonly',
        beforeAll: 'readonly',
        afterAll: 'readonly',
        beforeEach: 'readonly',
        afterEach: 'readonly'
      }
    },
    rules: {
      'no-undef': 'off', // Turn off for test files as vitest globals are defined above
      '@typescript-eslint/no-unused-expressions': 'off',
      'no-console': 'off', // Allow console in tests
      'react/prop-types': 'off',
      'jsx-a11y/no-noninteractive-element-to-interactive-role': 'off',
      'jsx-a11y/anchor-is-valid': 'off',
      'jsx-a11y/no-redundant-roles': 'off'
    }
  },

  // Storybook configuration
  ...storybook.configs['flat/recommended'],

  // Storybook stories - allow console logs and disable prop validation
  {
    files: ['**/*.stories.{js,jsx,ts,tsx}'],
    rules: {
      'no-console': 'off',
      'react/prop-types': 'off',
      'jsx-a11y/label-has-associated-control': 'off',
      'jsx-a11y/anchor-is-valid': 'off'
    }
  }
];
