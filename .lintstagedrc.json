{"*.py": ["black", "isort"], "*.{yml,yaml,json,md}": ["prettier --write"], ".github/**/*.{yml,yaml}": ["prettier --write"], ".github/scripts/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], ".github/scripts/**/*.{json,md}": ["prettier --write"], "backend/**/*.py": ["black", "isort"], "frontend/**/*.{css,scss,md,json}": ["prettier --write"], "frontend/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}