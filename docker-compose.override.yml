# Development-specific Docker Compose overrides
# This file is automatically loaded by docker-compose for development

services:
  backend:
    build:
      dockerfile: Dockerfile  # Use original Dockerfile for development
    environment:
      - DEBUG=True
      - DJANGO_SETTINGS_MODULE=config.settings
    command: python /app/scripts/run_with_reload.py
    volumes:
      - .:/app
      - /app/node_modules  # Exclude node_modules from volume sync
    ports:
      - "8000:8000"
    # Override healthcheck for faster development
    healthcheck:
      test: ["CMD", "sh", "-c", "python -c \"import urllib.request; urllib.request.urlopen('http://localhost:8000/').read()\""]
      interval: 60s
      timeout: 10s
      retries: 2
      start_period: 30s

  celery:
    build:
      dockerfile: Dockerfile  # Use original Dockerfile for development
    environment:
      - DEBUG=True
      - DJANGO_SETTINGS_MODULE=config.settings
    command: sh -c "celery -A config worker -l debug --concurrency=2"
    volumes:
      - .:/app

  db:
    # Development-specific MySQL optimizations (reduced resource usage)
    environment:
      MYSQL_INNODB_BUFFER_POOL_SIZE: 512M
      MYSQL_INNODB_LOG_FILE_SIZE: 128M
    command: >
      --innodb-buffer-pool-size=512M
      --innodb-redo-log-capacity=134217728
      --innodb-buffer-pool-instances=2
      --innodb-log-buffer-size=32M
      --innodb-flush-log-at-trx-commit=1
      --innodb-io-capacity=500
      --innodb-io-capacity-max=1000
      --innodb-read-io-threads=4
      --innodb-write-io-threads=4
      --max-connections=150
      --thread-cache-size=32
      --table-open-cache=2000
      --table-definition-cache=1000
      --tmp-table-size=64M
      --max-heap-table-size=64M
      --join-buffer-size=1M
      --sort-buffer-size=1M
      --read-buffer-size=512K
      --read-rnd-buffer-size=1M
      --binlog-cache-size=512K
      --slow-query-log=1
      --slow-query-log-file=/var/log/mysql/slow.log
      --long-query-time=0.5
      --log-queries-not-using-indexes=1
      --log-slow-admin-statements=1
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --sql-mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO
    ports:
      - "3306:3306"  # Expose for development tools

  redis:
    # Development-specific Redis configuration (reduced resource usage)
    command: >
      redis-server
      --appendonly yes
      --appendfsync everysec
      --maxmemory 512M
      --maxmemory-policy allkeys-lru
      --maxmemory-samples 5
      --timeout 300
      --tcp-keepalive 60
      --tcp-backlog 128
      --databases 16
      --save 900 1
      --save 300 10
      --save 60 1000
      --rdbcompression yes
      --rdbchecksum yes
      --logfile /var/log/redis/redis.log
      --loglevel notice
    ports:
      - "6379:6379"  # Expose for development tools
