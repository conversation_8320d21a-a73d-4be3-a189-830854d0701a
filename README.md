# XD Incentives

> A comprehensive Django-based member management system designed to handle complex organizational hierarchies, team management, and permission-based access control.

[![Django](https://img.shields.io/badge/Django-5.2.4-green.svg)](https://djangoproject.com/)
[![Python](https://img.shields.io/badge/Python-3.12-blue.svg)](https://python.org/)
[![MySQL](https://img.shields.io/badge/MySQL-8.4.5-orange.svg)](https://mysql.com/)
[![Docker](https://img.shields.io/badge/Docker-Compose-blue.svg)](https://docker.com/)
[![License](https://img.shields.io/badge/License-Private-red.svg)](#)

## 🚀 Quick Start

Get the application running in under 5 minutes:

```bash
# Clone the repository
git clone https://github.com/integritystl/xd-incentives
cd xd-incentives

# Create environment file
cp .env.example .env
# Edit .env with your configuration

# Start with full database
make quick-start

# Or fresh install
make fresh-start

# Access the application
open http://localhost:8000
```

**Default Credentials:**
- **Admin:** `admin` / `admin123`
- **Database:** `testu` / `testpw`

## 📋 Table of Contents

1. [Project Overview](#project-overview)
2. [Architecture](#architecture)
3. [Installation & Setup](#installation--setup)
4. [Development Guide](#development-guide)
5. [API Reference](#api-reference)
6. [Database Schema](#database-schema)
7. [Testing](#testing)
8. [Deployment](#deployment)
9. [Troubleshooting](#troubleshooting)
10. [Contributing](#contributing)

---

## Project Overview

XD Incentives is a comprehensive Django-based member management system designed to handle complex organizational hierarchies, team management, and permission-based access control.

### Key Features

- **Clerk Authentication Integration**: Primary authentication provider with JWT fallback
- **Hierarchical Organization Structure**: Complex manager-subordinate relationships
- **Team Management**: Multi-role team assignments and hierarchies
- **Dynamic Permissions**: JSON-based configurable role permissions
- **Two-Factor Authentication**: TOTP and SMS support via Clerk
- **Real-time Communication**: WebSocket support via Django Channels
- **Background Processing**: Celery-based task queue

### Technology Stack

| Category | Technology | Version | Purpose |
|----------|------------|---------|---------|
| **Backend** | Django | 5.2.4 | Web framework & API server |
| **Language** | Python | 3.12 | Backend runtime |
| **Database** | MySQL | 8.4.5 | Primary database |
| **Cache** | Redis | 8 | Caching & message broker |
| **Task Queue** | Celery | Latest | Background processing |
| **WebSocket** | Django Channels | Latest | Real-time communication |
| **Server** | Daphne | Latest | ASGI server |
| **Authentication** | Clerk SDK | Latest | Primary auth provider |
| **2FA** | django-otp | Latest | Two-factor authentication |
| **SMS** | Twilio | Latest | SMS delivery |
| **Frontend** | React | 19+ | UI framework with Tanstack Start |
| **Frontend Language** | TypeScript | 5+ | Type-safe JavaScript |
| **Build Tool** | Vite | 6+ | Fast build tool & dev server |
| **Styling** | Tailwind CSS | 4+ | Utility-first CSS framework |
| **UI Components** | Shadcn UI | Latest | Component library |
| **State Management** | Tanstack Query + Zustand | Latest | Server & client state |
| **Routing** | Tanstack Router | Latest | Type-safe routing |
| **Component Development** | Storybook | 9+ | Component documentation |
| **Testing** | Vitest | Latest | Unit & integration testing |
| **Container** | Docker | Latest | Containerization |

---

## Architecture

### Current Application Structure

```
xd-incentives/
├── apps/                    # Django applications (backend)
│   ├── api/                # REST API endpoints
│   ├── member/             # Core member management
│   ├── customer/           # Customer management
│   ├── testapp/            # WebSocket testing (TEMPORARY POC)
│   └── theme/              # Tailwind CSS theming (TEMPORARY POC)
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # React components
│   │   │   └── ui/         # Base UI components (Shadcn)
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API integration layer
│   │   ├── stores/         # Zustand stores
│   │   ├── utils/          # Utility functions
│   │   └── types/          # TypeScript definitions
│   ├── .storybook/         # Storybook configuration
│   └── stories/            # Component stories
├── config/                 # Django settings
├── data/                   # Database dumps
├── scripts/                # Utility scripts
├── static/                 # Static files
└── templates/              # Email templates
```

### Core Components

#### Backend (Django)

##### apps/member/
The heart of the backend system, handling all member-related functionality:
- **models.py**: Core data models (17 models)
- **views.py**: Member dashboard, profile management
- **clerk_auth.py**: Clerk SDK integration
- **admin.py**: Enhanced Django admin
- **forms.py**: Member forms and validation
- **management/commands/**: Data seeding commands

##### apps/api/
RESTful API implementation with JWT authentication:
- **v1/**: Versioned API endpoints
- **urls.py**: API routing (40+ endpoints)
- **views.py**: API view implementations
- **serializers**: Complex nested serialization

#### Frontend (React + TypeScript)

##### Component Architecture
- **src/components/ui/**: Base UI components built with Shadcn UI
  - Button, Input, Card, Modal, Loading components
  - TypeScript interfaces for all props
  - Accessible by default with ARIA attributes
- **src/components/layout/**: Layout components (Header, Sidebar)
- **src/components/forms/**: Form-specific components
- **src/components/data/**: Data display components

##### State Management
- **Tanstack Query**: Server state, caching, and API synchronization
- **Zustand**: Client-side state (auth, theme, app settings)
- **Context API**: Global state providers

##### Development Tools
- **Storybook**: Component development and documentation
- **Vite**: Fast development server and build tool
- **TypeScript**: Strict type checking for code quality
- **Vitest**: Unit and integration testing framework

#### Authentication Flow
1. **Primary**: Clerk authentication (Optional TOTP/SMS)
2. **Frontend**: React app manages auth state with Zustand
3. **API Communication**: JWT tokens via SimpleJWT
4. **Fallback**: Django AbstractUser (Member model)

### Docker Services

| Service | Container | Port | Purpose |
|---------|-----------|------|---------|
| backend | xd-backend | 8000 | Django API server |
| frontend | xd-frontend | 3000 | React development server |
| db | xd-mysql | 3306 | MySQL database |
| redis | xd-redis | 6379 | Cache/message broker |
| celery | xd-celery | - | Background worker |
| storybook | xd-storybook | 6006 | Component documentation |

---

## Installation & Setup

### Prerequisites

- **Docker & Docker Compose** - Required for running the application
- **Git** - For cloning the repository
- **Make** - For using convenience commands
- **MySQL Client** (optional) - For direct database access

### Environment Setup

1. **Clone the Repository**

   ```bash
   git clone https://github.com/integritystl/xd-incentives
   cd xd-incentives
   ```

2. **Configure Environment**

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

### Quick Start Commands

```bash
# Start with full database
make quick-start

# Fresh install (complete reset)
make fresh-start

# Daily development
make up       # Start services
make down     # Stop services
make logs     # View logs
```

### Database Setup

#### Database Seeding

```bash
make seed-full         # Full dump + sample data
make seed-fresh        # Complete reset + seed
make seed-minimal      # Schema only + sample data
```

#### Database Management

```bash
make backup-db         # Create timestamped backup
make restore-db        # Interactive restore
make mysql-cli         # Access MySQL directly
```

---

## Development Guide

### Common Commands

#### Docker Operations

```bash
make up         # Start services
make down       # Stop services
make logs       # View logs
make status     # Check service status
```

#### Backend (Django) Commands

```bash
# Run inside container
docker exec xd-backend sh -c "cd backend && python manage.py <command>"

# Common commands
make django-migrate         # Run migrations
make django-makemigrations  # Create migrations
make django-test           # Run all tests
make django-shell          # Django shell
make django-superuser      # Create superuser
make django-sample-data    # Create all sample data
```

#### Frontend (React) Commands

```bash
# Development
make frontend-dev           # Start React dev server
make frontend-build         # Build for production
make frontend-test          # Run frontend tests
make frontend-lint          # Lint TypeScript/React code

# Storybook
make storybook-dev          # Start Storybook dev server
make storybook-build        # Build Storybook for production

# Direct npm commands (inside container)
docker exec xd-frontend npm run dev        # Start Vite dev server
docker exec xd-frontend npm run build      # Production build
docker exec xd-frontend npm run test       # Run Vitest tests
docker exec xd-frontend npm run storybook  # Start Storybook
```

### Management Commands

Sample data generation:
- `create_sample_data` - Create sample members
- `setup_member_types` - Setup member types with permissions
- `setup_member_hierarchy` - Create hierarchy relationships
- `setup_teams_multi` - Create teams with memberships
- `create_sample_terms` - Create terms documents
- `create_sample_privacy` - Create privacy policies
- `create_sample_communications` - Create communications
- `create_sample_password_resets` - Create password resets

### Adding New Features

#### Backend Features
1. Check existing patterns in relevant app directory
2. Create/update models with proper relationships
3. Generate and apply migrations
4. Update serializers if API changes needed
5. Add management command for sample data
6. Write tests following existing patterns

#### Frontend Features
1. Design components in Storybook first
2. Create TypeScript interfaces for data structures
3. Build reusable UI components with Shadcn patterns
4. Implement API integration with Tanstack Query
5. Add routing with Tanstack Router
6. Write component tests with Vitest
7. Update component stories and documentation

### Working with Permissions

- Permissions are stored as JSON in MemberType model
- Use `member.member_type.permissions` to check permissions
- Page access controlled via `page_access` JSON field
- Feature flags in `feature_flags` field

---

## API Reference

### Authentication Endpoints

#### JWT Token
- `POST /api/token/` - Obtain JWT token pair
- `POST /api/token/refresh/` - Refresh access token

#### Clerk Integration
- `POST /api/clerk/auth/` - Clerk authentication
- `POST /api/clerk/sync/` - Sync Clerk user data
- `POST /api/clerk/webhook/` - Clerk webhooks

### Member Management

#### Member Operations
- `GET /api/member/details/` - Current member details
- `GET /api/members/` - List all members (admin)
- `GET /api/members/search/` - Search members
- `GET /api/member/profile/` - Member profile
- `PUT /api/member/profile/<id>/` - Update profile

#### Hierarchy & Organization
- `GET /api/organization-chart/` - Organization structure
- `GET /api/member/<id>/hierarchy/` - Member hierarchy
- `POST /api/member/<id>/hierarchy-management/` - Manage hierarchy
- `GET /api/hierarchy/search/` - Search hierarchies

### Team Management

- `GET /api/teams/` - List all teams
- `GET /api/teams/<id>/members/` - Team members
- `GET /api/teams/<id>/hierarchy/` - Team hierarchy
- `GET /api/member/<id>/teams/` - Member's teams

### Member Types & Permissions

- `GET /api/member-types/` - List member types
- `GET /api/member-types/<id>/` - Member type details

### Legal & Compliance

#### Terms & Conditions
- `GET /api/terms/` - Latest terms
- `POST /api/terms/accept/` - Accept terms
- `GET /api/terms/history/` - Acceptance history

#### Privacy Policy
- `GET /api/privacy/` - Latest privacy policy
- `POST /api/privacy/accept/` - Accept privacy policy
- `GET /api/privacy/history/` - Acceptance history

### Communication System

- `GET /api/communications/` - Member communications
- `GET /api/communications/<id>/` - Communication details
- `GET /api/communications/admin/` - Admin communications

### Password Management

- `POST /api/password-reset/request/` - Request reset
- `POST /api/password-reset/validate/` - Validate token
- `POST /api/password-reset/confirm/` - Confirm reset
- `GET /api/password-reset/history/` - Reset history
- `GET /api/password-reset/stats/` - Reset statistics

---

## Database Schema

### Core Models

#### Member (extends AbstractUser)
Primary user model with enhanced fields:
- Authentication: username, email, password, clerk_user_id
- Profile: first_name, last_name, status, tier
- Contact: work/home addresses, phone numbers
- Relations: region, member_type
- Approval: approved_by, approved_date, denied_by, denied_date
- Settings: lang, two_factor_auth_method, feature_flags

#### MemberType
Dynamic permission system with JSON configuration:
- Basic: name, slug, description, is_active
- JSON Fields:
  - permissions: List of permission strings
  - page_access: Page-level access control
  - navigation: Menu structure
  - feature_flags: Enabled features
  - dashboard_layout: Widget configuration
  - theme_settings: UI customization
- Access Control: can_signup, requires_approval, max_subordinates
- Relations: can_manage_types (self-referencing M2M)

#### MemberHierarchy
Complex organizational relationships:
- Relations: member, manager (both FK to Member)
- Types: direct_manager, mentor, supervisor, team_lead, project_manager
- Temporal: start_date, end_date
- Flags: is_primary

#### Team & MemberTeam
Team management with role-based membership:
- Team: name, team_type, description, team_lead, is_active
- MemberTeam: member, team, role, is_primary, start_date, end_date
- Roles: member, lead, admin, manager, contributor, observer, salesrep

### Key Relationships

```
Member ─┬─→ MemberType (N:1)
        ├─→ Region (N:1)
        ├─↔ Member (M:M via MemberHierarchy)
        └─↔ Team (M:M via MemberTeam)

MemberType ↔ MemberType (M:M - management hierarchy)
```

### Permission Configuration

MemberType permissions use JSON fields:

```json
{
  "permissions": ["admin.access", "member.view", "reports.generate"],
  "page_access": {
    "dashboard": {"access": true, "features": ["widgets", "analytics"]},
    "reports": {"access": false, "reason": "tier_restriction"}
  },
  "navigation": {
    "menu": [
      {"label": "Dashboard", "url": "/dashboard", "icon": "home"},
      {"label": "Members", "url": "/members", "icon": "users"}
    ]
  },
  "feature_flags": ["reports", "analytics", "export"]
}
```

---

## Testing

### Test Structure

```
apps/
├── member/tests/
│   ├── test_models.py
│   ├── test_views.py
│   └── test_api.py
└── api/tests/
    ├── test_authentication.py
    └── test_serializers.py
```

### Running Tests

#### Backend Tests (Django)

```bash
# All Django tests
make django-test

# Specific app
make django-test-app APP=member

# Specific test
docker exec xd-backend sh -c "cd backend && python manage.py test apps.member.tests.test_models"

# With coverage
docker exec xd-backend sh -c "cd backend && coverage run --source='.' manage.py test"
docker exec xd-backend coverage report
```

#### Frontend Tests (React + TypeScript)

```bash
# All frontend tests
make frontend-test

# Watch mode for development
docker exec xd-frontend npm run test:watch

# Component tests with UI
docker exec xd-frontend npm run test:ui

# Coverage report
docker exec xd-frontend npm run test:coverage

# Specific test file
docker exec xd-frontend npm run test -- Button.test.tsx
```

#### Component Testing with Storybook

```bash
# Start Storybook for visual testing
make storybook-dev

# Run Storybook tests
docker exec xd-frontend npm run test-storybook

# Build and test stories
docker exec xd-frontend npm run build-storybook
```

---

## Deployment

### Production Checklist

1. **Environment**
   - Set `DEBUG=False`
   - Generate strong `SECRET_KEY`
   - Configure `ALLOWED_HOSTS`
   - Set production database credentials

2. **Database**
   - Use managed MySQL service
   - Configure backups
   - Set up read replicas if needed

3. **Static Files**
   - Configure CDN
   - Use production web server (nginx)
   - Run `collectstatic`

4. **Security**
   - Enable HTTPS
   - Configure CORS
   - Set up rate limiting
   - Use production Redis

### Docker Production

```bash
# Build production image
docker build -f Dockerfile.multi-stage -t xd-incentives:prod .

# Run production container
docker run -d \
  -p 8000:8000 \
  -e DEBUG=False \
  -e SECRET_KEY=<production-key> \
  xd-incentives:prod
```

### Health Checks

- Application: `http://localhost:8000/status/`
- Database: `docker exec xd-mysql mysqladmin ping`
- Redis: `docker exec xd-redis redis-cli ping`

---

## Troubleshooting

### Common Issues

#### Port Already in Use

```bash
# Check what's using port 8000
lsof -i :8000
# Kill the process
```

#### Database Connection Issues

```bash
# Check if MySQL is running
docker-compose ps
# Check MySQL logs
docker-compose logs db
```

#### Permission Issues

```bash
# Make wait script executable
chmod +x scripts/wait-for-db.sh
```

#### Static Files Not Loading

```bash
# Collect static files
docker exec xd-backend sh -c "cd backend && python manage.py collectstatic --noinput"
```

### Reset Everything

```bash
# Stop and remove everything
make clean-all && make fresh-start
```

### Debugging Tips

- Check service status: `make status`
- View specific logs: `docker-compose logs -f <service>`
- Database issues: Check `docker-compose logs -f db`
- Permission denied: Ensure `scripts/wait-for-db.sh` is executable
- Clear everything: `make clean-all && make fresh-start`

---

## Contributing

### Development Workflow

1. **Fork the repository**
2. **Create a feature branch**

   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make your changes**
4. **Run tests**

   ```bash
   make django-test
   ```

5. **Commit your changes**

   ```bash
   git commit -m "Add your feature description"
   ```

6. **Push to your fork**

   ```bash
   git push origin feature/your-feature-name
   ```

7. **Create a Pull Request**

### Code Standards

- Follow PEP 8 for Python code
- Use meaningful variable and function names
- Add docstrings to functions and classes
- Write tests for new functionality
- Update documentation as needed

---

## Important URLs

### Development URLs
- **Frontend Application**: <http://localhost:3000/>
- **Backend API**: <http://localhost:8000/api/>
- **Django Admin**: <http://localhost:8000/admin/>
- **Storybook**: <http://localhost:6006/>
- **API Status**: <http://localhost:8000/status/>
- **WebSocket Test**: <http://localhost:8000/redistest/>

### Production URLs
- **Application**: TBD
- **API Documentation**: TBD
- **Component Library**: TBD (Storybook)

## Environment Configuration

### Required Variables
- `SECRET_KEY` - Django secret key
- `DB_NAME` - Database name
- `DB_USER` - Database username
- `DB_PASSWORD` - Database password
- `DB_HOST` - Database host
- `ALLOWED_HOSTS` - Allowed hostnames

### Optional Variables
- `DEBUG` - Debug mode (default: False)
- `CLERK_PUBLISHABLE_KEY` - Clerk public key
- `CLERK_SECRET_KEY` - Clerk secret key
- `TWO_FACTOR_SMS_GATEWAY` - SMS gateway (twilio)
- `TWILIO_ACCOUNT_SID` - Twilio account
- `TWILIO_AUTH_TOKEN` - Twilio auth
- `TWILIO_FROM_NUMBER` - SMS from number

## Additional Resources

### Documentation
- [API Documentation](./docs/api/)
- [Architecture Overview](./docs/architecture/)
- [Frontend Specification](./docs/architecture/front-end-spec.md)
- [Authentication Guide](./docs/auth/)
- [Database Schema](./docs/tooling/database.md)
- [Development Best Practices](./docs/guides/development-best-practices.md)
- [PRD & Technical Requirements](./docs/prd/)
- [Claude Code Instructions](./CLAUDE.md)

### Component Development
- **Storybook**: <http://localhost:6006/> (when running)
- **Component Library**: Built with Shadcn UI + Tailwind CSS
- **Design System**: Following WCAG 2.1 AA accessibility standards
- **TypeScript**: Strict type checking enabled

### GitHub Resources
- **Repository**: [integritystl/xd-incentives](https://github.com/integritystl/xd-incentives)
- **Issues**: Report bugs and feature requests
- **Pull Requests**: Contribute code changes
- **Actions**: CI/CD pipeline status

---

## License

This project is proprietary and confidential. All rights reserved - Integrity XD.

## Support

For issues, questions, or support:

1. **Check the logs**: `make logs`
2. **Review this documentation**
3. **Check existing issues** in the project repository
4. **Contact the development team**