# Notes / Logs
notes.txt
.bmad-*
logs
data/backups/
output/
docs/specs
docs/stories
docs/validation

# Python`
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Django
*.log
*.sql
local_settings.py
db.sqlite3
db.sqlite3-journal
media/

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Static files (if using collectstatic)
staticfiles/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Security - HTTP cookies and session data
cookies.txt

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Frontend build outputs
frontend/dist/
frontend/build/
frontend/.vite/

# Package manager locks (let developers choose)
package-lock.json
yarn.lock
pnpm-lock.yaml

# AI Tools
.windsurf/
.gemini/
GEMINI.md
CLAUDE.md
.claude
ai_docs/

.tanstack/
*.gen.ts

# Lighthouse
lighthouse-report.*
